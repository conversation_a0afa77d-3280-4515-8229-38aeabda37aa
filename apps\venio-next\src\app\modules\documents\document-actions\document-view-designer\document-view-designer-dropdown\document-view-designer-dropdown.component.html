<ng-template #designerContainerTpl></ng-template>
<button
  kendoButton
  #dropdownAnchor
  (click)="toggleDropdown()"
  themeColor="none"
  fillMode="outline"
  class="t-w-[18.75rem]">
  <div
    class="t-flex t-justify-between t-capitalize t-items-center"
    [ngStyle]="{ 'width.px': dropdownAnchor.element.clientWidth - 16 }">
    <span>{{ selectedDocumentViewDesignerDropdownItem()?.viewName }}</span>
    <kendo-svg-icon [icon]="downIcon"></kendo-svg-icon>
  </div>
</button>
@if (dropdownContentVisibility) {
<kendo-popup
  #itemPopup
  [popupClass]="'!t-rounded-none v-document-menu-popup-container !t-shadow-md'"
  [anchor]="dropdownAnchor.element">
  <div
    class="t-flex t-flex-col t-whitespace-nowrap t-max-h-[calc(100vh_-_10rem)] t-overflow-auto"
    [ngStyle]="{ 'width.px': dropdownAnchor.element.clientWidth - 20 }">
    <!-- View Management Actions in 2 Columns -->
    <div
      *ngIf="!isExternalUser() && hasViewManageRight()"
      class="t-flex t-justify-between t-items-center">
      <button
        kendoButton
        fillMode="clear"
        themeColor="none"
        size="none"
        class="t-text-[#1EBADC] t-text-[0.875rem] t-flex t-items-center t-flex-1 t-justify-start t-px-1"
        (click)="listItemClickAction(undefined, commonActionTypes.ADD, $event)">
        <kendo-svg-icon [icon]="iconPlus"></kendo-svg-icon>
        <span class="t-capitalize t-ml-1">Add New View</span>
      </button>

      <!-- View All Button -->
      <button
        kendoButton
        fillMode="clear"
        themeColor="none"
        size="none"
        class="t-text-primary t-text-[0.875rem] t-flex t-items-center t-flex-1 t-justify-end t-ml-2 t-px-2"
        (click)="viewAllViews()">
        <span class="t-capitalize">View All</span>
      </button>
    </div>

    <!-- List (Header + Items) -->
    <kendo-listview
      class="t-border-0 v-document-view-designer-list t-max-h-96"
      itemClass="v-document-view-item t-relative t-cursor-pointer t-px-2"
      [navigable]="true"
      [data]="loadedItems()"
      (scrollBottom)="loadMoreItems()"
      containerClass="k-d-flex k-flex-col k-flex-nowrap t-mt-2">
      <ng-template kendoListViewHeaderTemplate>
        <!-- Selected View Row -->
        <div
          class="t-w-full t-mb-3 t-relative t-flex t-justify-between t-items-center t-text-[#263238] t-text-sm t-border-dashed t-border-b t-border-[#ececeb]">
          <span class="t-truncate t-my-2">{{
            selectedDocumentViewDesignerDropdownItem()?.viewName
          }}</span>
          @if(selectedDocumentViewDesignerDropdownItem()?.allowToEdit &&
          hasViewManageRight()){
          <button
            kendoButton
            fillMode="none"
            themeColor="none"
            size="none"
            class="v-edit-box t-py-0.5 t-px-1"
            title="Edit Current View"
            (click)="
              listItemClickAction(
                selectedDocumentViewDesignerDropdownItem(),
                commonActionTypes.EDIT,
                $event
              )
            ">
            <kendo-svg-icon [icon]="iconPencil" size="small" />
          </button>
          }
        </div>
        <!-- Search -->
        <kendo-textbox
          #searchInput
          class="t-w-full t-mb-2"
          size="small"
          (valueChange)="updateViewBasedOnSearch()"
          placeholder="Search..."
          [clearButton]="true" />
      </ng-template>
      <ng-template kendoListViewItemTemplate let-dataItem="dataItem">
        <div
          (click)="
            listItemClickAction(dataItem, commonActionTypes.SELECT, $event)
          "
          class="t-flex t-justify-between t-items-center t-py-1 v-document-view-item v-document-menu-popup-item"
          (mouseleave)="toggleIconContainerOpaque(actionItem, false)"
          (mouseenter)="toggleIconContainerOpaque(actionItem, true)">
          <span> {{ dataItem.viewName }}</span>
          <span
            (click)="$event.stopPropagation()"
            class="v-document-view-designer-list-action-container t-min-w-4"
            #actionItem>
            <ng-container
              *ngIf="dataItem?.allowToEdit && hasViewManageRight()"
              [ngTemplateOutlet]="pencilIconTpl"
              [ngTemplateOutletContext]="{
                $implicit: actionItem.style.opacity === '1',
                dataItem
              }" />
          </span>
        </div>
      </ng-template>
    </kendo-listview>
  </div>
</kendo-popup>
}

<ng-template #pencilIconTpl let-isWhite let-dataItem="dataItem">
  <kendo-svg-icon
    [title]="'Edit ' + dataItem?.viewName"
    (click)="listItemClickAction(dataItem, commonActionTypes.EDIT, $event)"
    [id]="isWhite"
    [ngClass]="isWhite ? 't-text-[#FFFFFF]' : 't-text-[#4476FF]'"
    class="t-cursor-pointer"
    [icon]="iconPencil"></kendo-svg-icon>
</ng-template>

<!-- Shared Kendo Window Titlebar for View windows -->
<ng-template #viewWindowTitleBar let-win>
  <span class="k-window-title t-items-center t-text-primary t-text-lg">
    <button
      class="t-inline-block t-cursor-default t-pointer-events-none t-w-[35px] t-p-1.5 t-bg-[#F3F3F3] t-rounded-full t-mr-2 t-h-[35px] t-items-center"
      fillMode="clear"
      kendoButton
      imageUrl="assets/svg/icon-view-management.svg"></button>
    View
  </span>
  <button
    kendoWindowCloseAction
    [window]="win"
    class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-w-6 t-h-6 t-p-0"></button>
</ng-template>

<!-- Ensure there is a Window container available for WindowService -->
<div kendoWindowContainer></div>
