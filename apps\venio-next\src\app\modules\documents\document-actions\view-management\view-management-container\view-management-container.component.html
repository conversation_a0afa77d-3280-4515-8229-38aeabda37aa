@defer {
<form [formGroup]="viewFormGroup" class="t-flex t-flex-col t-h-full">
  <div class="t-flex t-flex-col t-w-full t-h-full t-overflow-hidden">
    <!-- Header with ViewToolbar Component -->
    <div #viewToolbar class="t-block t-w-full t-relative t-flex-shrink-0">
      <venio-view-toolbar
        [formGroup]="viewFormGroup"
        [showBackButton]="fromListing"
        (backClicked)="goBack()" />
    </div>

    <!-- Tabs Section -->
    <div
      class="t-flex t-flex-col t-gap-4 t-flex-1 t-min-h-0 t-overflow-hidden t-relative"
      [class.t-opacity-50]="!canConfigureTabs()">
      <kendo-tabstrip
        [keepTabContent]="true"
        (tabSelect)="onTabSelect($event.index)"
        class="t-flex-1 t-flex t-flex-col t-overflow-hidden">
        <!-- Fields Tab -->
        <kendo-tabstrip-tab title="Fields" [selected]="activeTab() === 0">
          <ng-template kendoTabContent>
            <venio-view-field-selector
              class="t-mt-2 t-w-full t-h-[calc(100%_-_61px)] t-flex t-flex-col t-overflow-hidden"
              [availableCases]="fieldsTabAvailableCases()"
              [selectedProjectIds]="fieldsTabSelectedProjectIds()"
              (selectedProjectIdsChange)="
                onFieldsTabCaseSelectionChange($event)
              "
              [baseFields]="fieldsForFieldsTab()"
              [selectedFields]="selectedFields()"
              (fieldSelectionChanged)="selectedFields.set($event)" />
          </ng-template>
        </kendo-tabstrip-tab>

        <!-- Conditions Tab -->
        <kendo-tabstrip-tab title="Conditions" [selected]="activeTab() === 1">
          <ng-template kendoTabContent>
            <venio-view-conditions-builder class="t-mt-2" />
          </ng-template>
        </kendo-tabstrip-tab>

        <!-- Sorting Tab -->
        <kendo-tabstrip-tab title="Sorting" [selected]="activeTab() === 2">
          <ng-template kendoTabContent>
            <venio-view-sort-builder
              class="t-mt-2 t-w-full t-block"
              [fieldsInput]="fieldsForSortTab()" />
          </ng-template>
        </kendo-tabstrip-tab>
      </kendo-tabstrip>
      <!-- Interaction blocker overlay until required inputs are provided -->
      <div
        *ngIf="!canConfigureTabs()"
        class="t-absolute t-inset-0 t-z-10 t-bg-transparent t-cursor-not-allowed"
        aria-hidden="true"></div>
    </div>
  </div>
</form>
} @placeholder {
<!-- Tabs skeleton -->
<div class="t-flex t-flex-col t-gap-4 t-flex-1 t-min-h-0 t-overflow-hidden">
  <!-- Tab headers -->
  <div
    class="t-flex t-flex-row t-gap-1 t-border-b t-border-dashed t-border-[#E0E0E0]">
    <kendo-skeleton
      shape="text"
      width="80px"
      height="40px"
      class="t-mr-4"></kendo-skeleton>
    <kendo-skeleton
      shape="text"
      width="100px"
      height="40px"
      class="t-mr-4"></kendo-skeleton>
    <kendo-skeleton shape="text" width="80px" height="40px"></kendo-skeleton>
  </div>

  <!-- Fields tab content skeleton -->
  <div class="t-flex t-flex-col t-flex-1 t-min-h-0">
    <!-- Two column layout skeleton -->
    <div class="t-grid lg:t-grid-cols-2 t-gap-4 lg:t-gap-8 t-flex-1">
      <!-- Left column (Unselected fields) -->
      <div class="t-flex t-flex-col t-gap-2">
        <kendo-skeleton shape="text" width="150px" height="24px" />
        <kendo-skeleton
          shape="rectangle"
          width="100%"
          height="300px"
          class="t-rounded" />
      </div>

      <!-- Right column (Selected fields) -->
      <div class="t-flex t-flex-col t-gap-2">
        <kendo-skeleton
          shape="text"
          width="120px"
          height="24px"
          class="t-ml-6" />
        <kendo-skeleton
          shape="rectangle"
          width="100%"
          height="300px"
          class="t-rounded" />
      </div>
    </div>
  </div>
</div>
}
<!-- Footer Actions -->
<div
  class="t-flex t-w-full t-gap-3 t-justify-end t-pt-4 t-bottom-[0] t-left-[0px] t-bg-[#FFFFFF] t-p-4 t-fixed t-z-10">
  <button
    kendoButton
    class="v-custom-secondary-button"
    themeColor="secondary"
    fillMode="outline"
    (click)="save()"
    [disabled]="!isFormValid || isSaving() || !canConfigureTabs()">
    <kendo-loader *ngIf="isSaving()" size="small"></kendo-loader>
    <span *ngIf="!isSaving()">SAVE</span>
  </button>
  <button
    kendoButton
    themeColor="dark"
    fillMode="outline"
    (click)="cancel()"
    [disabled]="isSaving()">
    CANCEL
  </button>
</div>
