import { createAction, props } from '@ngrx/store'
import {
  ConditionGroup,
  ResponseModel,
  SearchFieldModel,
} from '@venio/shared/models/interfaces'
import { DocumentViewState } from './document-view.reducer'
import { ViewModel } from '@venio/data-access/review'

export enum DocumentViewActionTypes {
  // Actions for Fetch Document View
  FetchDocumentViewList = '[Document View] Fetch Document View List',
  FetchDocumentViewListSuccess = '[Document View] Fetch Document View List: Success',
  FetchDocumentViewListFailure = '[Document View] Fetch Document View List: Failure',

  // Actions for fetch search fields
  FetchSearchFields = '[Document View] Fetch Search Fields',
  FetchSearchFieldsSuccess = '[Document View] Fetch Search Fields: Success',
  FetchSearchFieldsFailure = '[Document View] Fetch Search Fields: Failure',

  // Actions for fetch view by viewId
  FetchViewByViewId = '[Document View] Fetch View By View Id',
  FetchViewByViewIdSuccess = '[Document View] Fetch View By View Id: Success',
  FetchViewByViewIdFailure = '[Document View] Fetch View By View Id: Failure',

  // Actions for store selected document view
  StoreSelectedDocumentView = '[Document View] Store Selected Document View',

  // Action fro store current form data
  StoreCurrentFormData = '[Document View] Store Current Form Data',
  storeConditionGroup = '[Document View] Store Condition Group',

  // Actions for document view CRUD operations
  AddOrUpdateView = '[Document View] Add Or Update View',
  AddOrUpdateViewSuccess = '[Document View] Add Or Update View: Success',
  AddOrUpdateViewFailure = '[Document View] Add Or Update View: Failure',

  DeleteDocumentView = '[Document View] Delete Document View',
  DeleteDocumentViewSuccess = '[Document View] Delete Document View: Success',
  DeleteDocumentViewFailure = '[Document View] Delete Document View: Failure',

  // Bulk delete views
  DeleteDocumentViews = '[Document View] Delete Document Views',
  DeleteDocumentViewsSuccess = '[Document View] Delete Document Views: Success',
  DeleteDocumentViewsFailure = '[Document View] Delete Document Views: Failure',

  // Resetting Document View  State
  ResetDocumentViewState = '[Document View] Reset State',
}

export const resetDocumentViewState = createAction(
  DocumentViewActionTypes.ResetDocumentViewState,
  props<{
    stateKey: keyof DocumentViewState | Array<keyof DocumentViewState>
  }>()
)

export const fetchDocumentViewList = createAction(
  DocumentViewActionTypes.FetchDocumentViewList,
  props<{ projectId?: number }>()
)

export const fetchDocumentViewListSuccess = createAction(
  DocumentViewActionTypes.FetchDocumentViewListSuccess,
  props<{ documentViewListSuccessResponse: ResponseModel }>()
)

export const fetchDocumentViewListFailure = createAction(
  DocumentViewActionTypes.FetchDocumentViewListFailure,
  props<{ documentViewListErrorResponse: ResponseModel }>()
)

export const storeSelectedDocumentView = createAction(
  DocumentViewActionTypes.StoreSelectedDocumentView,
  props<{ selectedDocumentView: ViewModel }>()
)

export const storeCurrentFormData = createAction(
  DocumentViewActionTypes.StoreCurrentFormData,
  props<{ currentFormData: Partial<ViewModel> }>()
)

export const fetchSearchFields = createAction(
  DocumentViewActionTypes.FetchSearchFields
)

export const fetchSearchFieldsSuccess = createAction(
  DocumentViewActionTypes.FetchSearchFieldsSuccess,
  props<{ searchFields: SearchFieldModel }>()
)

export const fetchSearchFieldsFailure = createAction(
  DocumentViewActionTypes.FetchSearchFieldsFailure,
  props<{ searchFieldsErrorResponse: ResponseModel }>()
)

export const addOrUpdateView = createAction(
  DocumentViewActionTypes.AddOrUpdateView,
  props<{ view: ViewModel; shouldLoadAndApply?: boolean }>()
)

export const addOrUpdateViewSuccess = createAction(
  DocumentViewActionTypes.AddOrUpdateViewSuccess,
  props<{ addOrUpdateViewSuccessResponse: ResponseModel }>()
)

export const addOrUpdateViewFailure = createAction(
  DocumentViewActionTypes.AddOrUpdateViewFailure,
  props<{ addOrUpdateViewErrorResponse: ResponseModel }>()
)

export const deleteDocumentView = createAction(
  DocumentViewActionTypes.DeleteDocumentView,
  props<{ viewId: number }>()
)

export const deleteDocumentViewSuccess = createAction(
  DocumentViewActionTypes.DeleteDocumentViewSuccess,
  props<{ deleteDocumentViewSuccessResponse: ResponseModel }>()
)

export const deleteDocumentViewFailure = createAction(
  DocumentViewActionTypes.DeleteDocumentViewFailure,
  props<{ deleteDocumentViewErrorResponse: ResponseModel }>()
)

export const deleteDocumentViews = createAction(
  DocumentViewActionTypes.DeleteDocumentViews,
  props<{ viewIds: number[] }>()
)

export const deleteDocumentViewsSuccess = createAction(
  DocumentViewActionTypes.DeleteDocumentViewsSuccess,
  props<{ deleteDocumentViewsSuccessResponse: ResponseModel }>()
)

export const deleteDocumentViewsFailure = createAction(
  DocumentViewActionTypes.DeleteDocumentViewsFailure,
  props<{ deleteDocumentViewsErrorResponse: ResponseModel }>()
)

export const fetchViewByViewId = createAction(
  DocumentViewActionTypes.FetchViewByViewId,
  props<{ viewId: number }>()
)

export const fetchViewByViewIdSuccess = createAction(
  DocumentViewActionTypes.FetchViewByViewIdSuccess,
  props<{ viewByViewIdSuccessResponse: ViewModel }>()
)

export const fetchViewByViewIdFailure = createAction(
  DocumentViewActionTypes.FetchViewByViewIdFailure,
  props<{ viewByViewIdErrorResponse: ResponseModel }>()
)

export const storeConditionGroup = createAction(
  DocumentViewActionTypes.storeConditionGroup,
  props<{ conditionGroup: ConditionGroup[] }>()
)
