<!-- Toolbar - Responsive, left/right alignment -->
<div
  class="t-flex t-flex-col lg:t-flex-row t-w-full t-p-4 lg:t-px-0 lg:t-py-0 t-gap-3 lg:t-gap-6"
  [formGroup]="formGroup()">
  <!-- Left: Back + Create + Name -->
  <div class="t-flex t-items-center t-gap-3 t-flex-wrap">
    <!-- Back Button -->
    @if (showBackButton()) {
    <button
      kendoButton
      type="button"
      fillMode="outline"
      (click)="onBackClick()"
      class="t-w-[74px] t-p-0 t-text-[16px] hover:t-text-[#000000] t-flex t-items-center">
      <span class="t-flex">
        <kendo-svg-icon
          class="t-text-[var(--v-custom-sky-blue)] hover:t-text-[var(--v-custom-sky-blue)]"
          [icon]="icons.chevronLeftIcon"
          size="large" />
        <span>Back</span>
      </span>
    </button>
    }

    <!-- Create View label -->
    <span class="t-font-semibold t-text-[16px] t-whitespace-nowrap"
      >{{ isLocked() ? 'Edit' : 'Create' }} View</span
    >

    <!-- Name Input next to Create View -->
    <div class="t-flex t-flex-col t-gap-1 t-w-[200px]">
      <div
        class="v-infield"
        [class.v-has-value]="!!formGroup().get('viewName')?.value">
        <label class="sr-only" for="view-name">Name</label>
        <span class="v-infield-label" aria-hidden="true">
          <span>Name</span><span class="v-asterisk">*</span>
        </span>
        <kendo-textbox
          id="view-name"
          formControlName="viewName"
          placeholder="Name" />
      </div>
    </div>
  </div>

  <!-- Right: ME + Dropdowns -->
  <div
    class="t-flex t-items-center t-gap-4 t-justify-end t-flex-1 t-flex-wrap"
    [ngClass]="{ 't-opacity-60': isLocked() && isPrivate() }">
    <!-- ME Toggle -->
    <div
      class="t-flex t-items-center t-gap-2"
      [ngClass]="{ 't-opacity-40': isLocked() }">
      <kendo-label
        class="t-justify-between t-flex t-font-semibold t-items-center t-text-sm t-text-[#000000]"
        text="ME">
        <kendo-switch
          formControlName="isPrivate"
          offLabel=""
          onLabel=""
          class="t-mx-1"
          size="large" />
      </kendo-label>
    </div>

    <!-- Client Dropdown -->
    <div class="t-flex t-flex-col t-gap-1 t-w-[224px]">
      <div
        class="v-infield v-static-label"
        [class.v-has-value]="!!formGroup().get('client')?.value?.length">
        <span class="v-infield-label" aria-hidden="true">
          <span>Select Client</span><span class="v-asterisk">*</span>
        </span>
        <kendo-multiselecttree
          #multiSelectClientTree
          id="client-select"
          kendoMultiSelectTreeSummaryTag
          formControlName="client"
          [kendoMultiSelectTreeFlatBinding]="clients()"
          idField="clientId"
          parentIdField="parentId"
          textField="clientName"
          valueField="clientId"
          [valuePrimitive]="false"
          placeholder="Select Client"
          [filterable]="true"
          (filterChange)="onFilterChange('client', $event)"
          [popupSettings]="{
            popupClass: 'custom-multiselecttree-popup t-w-80'
          }"
          (opened)="onDropdownOpened(multiSelectClientTree)"
          class="!t-w-56 custom-multiselecttree">
          <ng-template kendoMultiSelectTreeHeaderTemplate>
            <label
              class="t-flex t-items-center t-gap-2 t-pl-2"
              [ngClass]="{
                't-text-primary t-font-medium': isAllChecked('client')
              }"
              [for]="'checkAllClient'"
              (click)="$event.stopImmediatePropagation()">
              <input
                kendoCheckBox
                id="checkAllClient"
                type="checkbox"
                [checked]="isAllChecked('client')"
                [indeterminate]="isIndeterminate('client')"
                (change)="
                  toggleCheckAll('client', multiSelectClientTree, $event)
                " />
              Check all
            </label>
          </ng-template>
        </kendo-multiselecttree>
      </div>
    </div>

    <!-- Case Dropdown -->
    <div class="t-flex t-flex-col t-gap-1 t-w-[224px]">
      <div
        class="v-infield v-static-label"
        [class.v-has-value]="!!formGroup().get('case')?.value?.length">
        <label class="sr-only" for="case-select">Select Case</label>
        <span class="v-infield-label" aria-hidden="true">
          <span>Select Case</span><span class="v-asterisk">*</span>
        </span>
        <kendo-multiselecttree
          #multiSelectCaseTree
          id="case-select"
          kendoMultiSelectTreeSummaryTag
          formControlName="case"
          [kendoMultiSelectTreeFlatBinding]="projects()"
          idField="projectId"
          parentIdField="parentId"
          textField="projectName"
          valueField="projectId"
          [valuePrimitive]="false"
          placeholder="Select Case"
          [filterable]="true"
          [loading]="isCasesLoading()"
          (filterChange)="onFilterChange('case', $event)"
          [popupSettings]="{
            popupClass: 'custom-multiselecttree-popup t-w-80'
          }"
          (opened)="onDropdownOpened(multiSelectCaseTree)"
          class="!t-w-56 custom-multiselecttree">
          <ng-template kendoMultiSelectTreeHeaderTemplate>
            <label
              class="t-flex t-items-center t-gap-2 t-pl-2"
              [ngClass]="{
                't-text-primary t-font-medium': isAllChecked('case')
              }"
              [for]="'checkAllCase'"
              (click)="$event.stopImmediatePropagation()">
              <input
                kendoCheckBox
                id="checkAllCase"
                type="checkbox"
                [checked]="isAllChecked('case')"
                [indeterminate]="isIndeterminate('case')"
                (change)="
                  toggleCheckAll('case', multiSelectCaseTree, $event)
                " />
              Check all
            </label>
          </ng-template>
        </kendo-multiselecttree>
      </div>
    </div>

    <!-- Group Dropdown -->
    <div class="t-flex t-flex-col t-gap-1 t-w-[224px]">
      <div
        class="v-infield v-static-label"
        [class.v-has-value]="!!formGroup().get('group')?.value?.length">
        <label class="sr-only" for="group-select">Select Group</label>
        <span class="v-infield-label" aria-hidden="true">
          <span>Select Group</span><span class="v-asterisk">*</span>
        </span>
        <kendo-multiselecttree
          #multiSelectGroupTree
          id="group-select"
          kendoMultiSelectTreeSummaryTag
          formControlName="group"
          [kendoMultiSelectTreeFlatBinding]="groups()"
          idField="groupId"
          parentIdField="parentId"
          textField="groupName"
          valueField="groupId"
          [valuePrimitive]="false"
          placeholder="Select Group"
          [filterable]="true"
          [loading]="isGroupsLoading()"
          (filterChange)="onFilterChange('userGroup', $event)"
          [popupSettings]="{
            popupClass: 'custom-multiselecttree-popup t-w-80'
          }"
          (opened)="onDropdownOpened(multiSelectGroupTree)"
          class="!t-w-56 custom-multiselecttree">
          <ng-template kendoMultiSelectTreeHeaderTemplate>
            <label
              class="t-flex t-items-center t-gap-2 t-pl-2"
              [ngClass]="{
                't-text-primary t-font-medium': isAllChecked('userGroup')
              }"
              [for]="'checkAllUserGroup'"
              (click)="$event.stopImmediatePropagation()">
              <input
                kendoCheckBox
                id="checkAllUserGroup"
                type="checkbox"
                [checked]="isAllChecked('userGroup')"
                [indeterminate]="isIndeterminate('userGroup')"
                (change)="
                  toggleCheckAll('userGroup', multiSelectGroupTree, $event)
                " />
              Check all
            </label>
          </ng-template>
        </kendo-multiselecttree>
      </div>
    </div>
  </div>
</div>
