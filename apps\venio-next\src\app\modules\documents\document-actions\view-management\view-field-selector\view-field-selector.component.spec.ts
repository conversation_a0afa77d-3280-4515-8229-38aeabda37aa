import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ViewFieldSelectorComponent } from './view-field-selector.component'
import { Field, ProjectInfo, FieldFacade } from '@venio/data-access/review'
import { DocumentViewFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import { provideMockStore } from '@ngrx/store/testing'

describe('ViewFieldSelectorComponent', () => {
  let component: ViewFieldSelectorComponent
  let fixture: ComponentFixture<ViewFieldSelectorComponent>
  let mockDocumentViewFacade: Partial<DocumentViewFacade>
  let mockFieldFacade: Partial<FieldFacade>

  const mockSystemFields: Field[] = [
    {
      venioFieldId: 1,
      internalFieldName: 'system_field_1',
      displayFieldName: 'System Field 1',
      allowNullSearch: true,
      fieldDataType: 'string',
      fieldGroup: 'Common',
      searchDataType: 'string',
      isCustomField: false,
      allowCoding: false,
      isSearchableField: true,
      id: 1,
    },
    {
      venioFieldId: 2,
      internalFieldName: 'system_field_2',
      displayFieldName: 'System Field 2',
      allowNullSearch: true,
      fieldDataType: 'string',
      fieldGroup: 'Common',
      searchDataType: 'string',
      isCustomField: false,
      allowCoding: false,
      isSearchableField: true,
      id: 2,
    },
  ]

  type FieldWithProject = Field & { projectId?: number }
  const mockCustomFields: FieldWithProject[] = [
    {
      venioFieldId: 101,
      internalFieldName: 'custom_field_1',
      displayFieldName: 'Custom Field 1',
      allowNullSearch: true,
      fieldDataType: 'string',
      fieldGroup: 'Custom',
      searchDataType: 'string',
      isCustomField: true,
      allowCoding: true,
      isSearchableField: true,
      id: 101,
      projectId: 1,
    } as unknown as FieldWithProject,
    {
      venioFieldId: 102,
      internalFieldName: 'custom_field_2',
      displayFieldName: 'Custom Field 2',
      allowNullSearch: true,
      fieldDataType: 'string',
      fieldGroup: 'Custom',
      searchDataType: 'string',
      isCustomField: true,
      allowCoding: true,
      isSearchableField: true,
      id: 102,
      projectId: 2,
    } as unknown as FieldWithProject,
  ]

  const mockProjects: ProjectInfo[] = [
    {
      projectId: 1,
      projectName: 'Project 1',
      allowTiff: true,
      clientId: 1,
      enableNativeAutoPrefetch: true,
    },
    {
      projectId: 2,
      projectName: 'Project 2',
      allowTiff: true,
      clientId: 1,
      enableNativeAutoPrefetch: true,
    },
    {
      projectId: 3,
      projectName: 'Project 3',
      allowTiff: true,
      clientId: 1,
      enableNativeAutoPrefetch: true,
    },
  ]

  beforeEach(async () => {
    // Silence Kendo Grid virtual scrolling warnings in tests
    jest.spyOn(console, 'warn').mockImplementation((): void => {})
    mockDocumentViewFacade = {
      storeCurrentFormData: jest.fn(),
      selectCurrentFormData$: of(null),
    }

    mockFieldFacade = {
      fetchAllVenioFields: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [ViewFieldSelectorComponent],
      providers: [
        { provide: DocumentViewFacade, useValue: mockDocumentViewFacade },
        { provide: FieldFacade, useValue: mockFieldFacade },
        provideHttpClient(),
        provideNoopAnimations(),
        provideMockStore({}),
      ],
    })
      // Remove heavy Kendo Grid template to avoid virtual scroll warnings and focus on logic
      .overrideComponent(ViewFieldSelectorComponent, {
        set: { template: '<ng-content></ng-content>' },
      })
      .compileComponents()

    fixture = TestBed.createComponent(ViewFieldSelectorComponent)
    component = fixture.componentInstance
  })

  afterEach(() => {
    const spy = console.warn as unknown as { mockRestore?: () => void }
    spy.mockRestore?.()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  describe('Project-based field management', () => {
    it('should show all system fields when no projects are selected', () => {
      fixture.componentRef.setInput('baseFields', mockSystemFields)
      fixture.componentRef.setInput('selectedProjectIds', [])
      fixture.detectChanges()

      const availableFields = component.availableFieldsForSelectedProjects()
      expect(availableFields).toHaveLength(2)
      expect(availableFields.every((field) => !field.isCustomField)).toBe(true)
    })

    it('should show system fields and custom fields for selected projects', () => {
      fixture.componentRef.setInput('baseFields', [
        ...mockSystemFields,
        ...mockCustomFields,
      ])
      fixture.componentRef.setInput('selectedProjectIds', [1])
      fixture.detectChanges()

      const availableFields = component.availableFieldsForSelectedProjects()
      expect(availableFields).toHaveLength(3) // 2 system + 1 custom from project 1
      expect(
        availableFields.filter((field) => field.isCustomField)
      ).toHaveLength(1)
    })

    it('should show custom fields from multiple selected projects', () => {
      fixture.componentRef.setInput('baseFields', [
        ...mockSystemFields,
        ...mockCustomFields,
      ])
      fixture.componentRef.setInput('selectedProjectIds', [1, 2])
      fixture.detectChanges()

      const availableFields = component.availableFieldsForSelectedProjects()
      expect(availableFields).toHaveLength(4) // 2 system + 2 custom fields
      expect(
        availableFields.filter((field) => field.isCustomField)
      ).toHaveLength(2)
    })

    it('should update available fields when projects are unselected', () => {
      fixture.componentRef.setInput('baseFields', [
        ...mockSystemFields,
        ...mockCustomFields,
      ])
      fixture.componentRef.setInput('selectedProjectIds', [1, 2])
      fixture.detectChanges()

      // Add some fields to the right grid
      const customField1 = mockCustomFields[0]
      const customField2 = mockCustomFields[1]
      component.rightFields.set({
        data: [
          {
            displayFieldName: customField1.displayFieldName,
            venioFieldId: customField1.venioFieldId,
            isCustomField: customField1.isCustomField,
            fieldDisplayOrder: 0,
            projectId: customField1.projectId,
            sourceProjects: [customField1.projectId],
          } as unknown as Field,
          {
            displayFieldName: customField2.displayFieldName,
            venioFieldId: customField2.venioFieldId,
            isCustomField: customField2.isCustomField,
            fieldDisplayOrder: 0,
            projectId: customField2.projectId,
            sourceProjects: [customField2.projectId],
          } as unknown as Field,
        ],
        total: 2,
      })

      // Unselect project 2
      fixture.componentRef.setInput('selectedProjectIds', [1])
      fixture.detectChanges()

      // Field from project 2 should no longer be available
      const available = component.availableFieldsForSelectedProjects()
      expect(available.some((f) => f.projectId === 2)).toBe(false)
    })

    it('should handle field deduplication correctly', () => {
      // Create fields with same name but different projects
      const duplicateCustomFields: Field[] = [
        {
          venioFieldId: 201,
          internalFieldName: 'duplicate_field',
          displayFieldName: 'Duplicate Field',
          allowNullSearch: true,
          fieldDataType: 'string',
          fieldGroup: 'Custom',
          searchDataType: 'string',
          isCustomField: true,
          allowCoding: true,
          isSearchableField: true,
          id: 201,
          projectId: 1,
        } as unknown as Field,
        {
          venioFieldId: 202,
          internalFieldName: 'duplicate_field',
          displayFieldName: 'Duplicate Field',
          allowNullSearch: true,
          fieldDataType: 'string',
          fieldGroup: 'Custom',
          searchDataType: 'string',
          isCustomField: true,
          allowCoding: true,
          isSearchableField: true,
          id: 202,
          projectId: 2,
        } as unknown as Field,
      ]

      fixture.componentRef.setInput('baseFields', [
        ...mockSystemFields,
        ...duplicateCustomFields,
      ])
      fixture.componentRef.setInput('selectedProjectIds', [1, 2])
      fixture.detectChanges()

      const availableFields = component.availableFieldsForSelectedProjects()
      const duplicateFields = availableFields.filter(
        (field) => field.displayFieldName === 'Duplicate Field'
      )

      // In this component, duplicates are kept separate (container handles merging)
      expect(duplicateFields).toHaveLength(2)
    })
  })

  describe('Field selection and movement', () => {
    beforeEach(() => {
      fixture.componentRef.setInput('baseFields', [
        ...mockSystemFields,
        ...mockCustomFields,
      ])
      fixture.componentRef.setInput('selectedProjectIds', [1])
      fixture.detectChanges()
    })

    it('should move fields from left to right grid', () => {
      const initialRightFieldsCount = component.rightFields().data.length
      const systemField = mockSystemFields[0]

      // Simulate selecting a field from left grid
      const mockEvent = {
        selectedRows: [
          {
            dataItem: {
              displayFieldName: systemField.displayFieldName,
              venioFieldId: systemField.venioFieldId,
              isCustomField: systemField.isCustomField,
              fieldDisplayOrder: 0,
              projectId: undefined,
              sourceProjects: [],
            },
          },
        ],
        deselectedRows: [],
      }

      component.onSelectionChangeAvailableFields(mockEvent)

      expect(component.rightFields().data).toHaveLength(
        initialRightFieldsCount + 1
      )
      expect(
        component
          .rightFields()
          .data.some(
            (field) => field.displayFieldName === systemField.displayFieldName
          )
      ).toBe(true)
    })

    it('should remove fields from right grid when deselected', () => {
      const systemField = mockSystemFields[0]

      // Add field to right grid first
      component.rightFields.set({
        data: [
          {
            displayFieldName: systemField.displayFieldName,
            venioFieldId: systemField.venioFieldId,
            isCustomField: systemField.isCustomField,
            fieldDisplayOrder: 0,
            projectId: undefined,
            sourceProjects: [],
          } as unknown as Field,
        ],
        total: 1,
      })

      // Simulate deselecting the field
      const mockEvent = {
        selectedRows: [],
        deselectedRows: [
          {
            dataItem: {
              displayFieldName: systemField.displayFieldName,
              venioFieldId: systemField.venioFieldId,
              isCustomField: systemField.isCustomField,
              fieldDisplayOrder: 0,
              projectId: undefined,
              sourceProjects: [],
            },
          },
        ],
      }

      component.onSelectionChangeAvailableFields(mockEvent)

      expect(component.rightFields().data).toHaveLength(0)
    })
  })

  describe('Computed properties', () => {
    beforeEach(() => {
      fixture.componentRef.setInput('baseFields', [
        ...mockSystemFields,
        ...mockCustomFields,
      ])
      fixture.componentRef.setInput('selectedProjectIds', [1])
      fixture.detectChanges()
    })

    it('should compute leftFields correctly', () => {
      const leftFields = component.leftFields().data
      expect(leftFields.length).toBeGreaterThan(0)

      // All left fields should not be in right fields
      const rightFieldNames = new Set(
        component.rightFields().data.map((f) => f.displayFieldName)
      )
      expect(
        leftFields.every(
          (field) => !rightFieldNames.has(field.displayFieldName)
        )
      ).toBe(true)
    })

    it('should filter fields by search term', () => {
      component.searchTerm.set('System')
      fixture.detectChanges()

      const filteredFields = component.filteredFields()
      expect(
        filteredFields.every(
          (field) =>
            field.displayName.toLowerCase().includes('system') ||
            field.fieldName.toLowerCase().includes('system')
        )
      ).toBe(true)
    })

    it('should filter fields by category', () => {
      component.selectedCategory.set('Custom Fields')
      fixture.detectChanges()

      const filteredFields = component.filteredFields()
      expect(filteredFields.every((field) => field.isCustomField)).toBe(true)
    })
  })

  describe('Case selection', () => {
    beforeEach(() => {
      fixture.componentRef.setInput('availableCases', mockProjects)
      fixture.detectChanges()
    })

    it('should handle case selection changes', () => {
      const selectedCases = [mockProjects[0], mockProjects[1]]
      component.onFieldsTabCasesChange(selectedCases)
      // simulate parent updating the input with emitted ids
      const ids = selectedCases.map((c) => c.projectId)
      fixture.componentRef.setInput('selectedProjectIds', ids)
      fixture.detectChanges()
      expect(component.selectedProjectsAsItems).toEqual(selectedCases)
    })

    it('should handle select all functionality', () => {
      const mockEvent = { target: { checked: true } }
      component.onSelectAllChange(mockEvent)
      // simulate parent updating to all ids
      const allIds = mockProjects.map((p) => p.projectId)
      fixture.componentRef.setInput('selectedProjectIds', allIds)
      fixture.detectChanges()
      expect(component.selectedProjectsAsItems).toHaveLength(
        mockProjects.length
      )
    })

    // Filtering is internal; selection helpers cover behavior via selectedProjectsAsItems
  })
})
