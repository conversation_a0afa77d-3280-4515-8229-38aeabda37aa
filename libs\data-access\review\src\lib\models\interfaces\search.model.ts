import { ReviewViewType } from '../constants/search.enums'
import { DocSelectionTypeEnum } from './responsive-pst.model'
export interface SearchRequestModel {
  includePC: boolean

  lstMedia: string[]

  lstFolder?: number[]

  projectId: string

  searchExpression: string

  searchGuid: string

  userId?: string

  userType: string

  isPageInit?: boolean

  baseGUID: string

  isForwardFilter: boolean

  reviewSetId?: number

  batchId?: number

  searchDuplicateOption?: SearchDupOption

  sortFieldModel?: SortingFieldModel[]

  dynamicFolderId?: number

  isDynamicFolderGlobal?: boolean

  projectLoginId?: number

  viewType?: ReviewViewType

  isLoadFile?: boolean

  isSavedSearch?: boolean

  isSearchFromHistory?: boolean

  searchId?: number

  tempTableResponse?: TempTableResponseModel

  docCount?: number

  formState?: string

  documentShareToken?: string

  isSqlMode?: boolean

  searchQuery?: string

  navigationType?: NavigationType

  isFilterSearch?: boolean

  dynamicFolderScope?: string

  showOnlyInclusiveEmailsThreads?: boolean

  viewTagRuleConflictFiles?: ViewTagRuleConflictModel

  reviewBatchAfterCALThreshold?: boolean
}

export interface SearchRequestForBulkRedactModel {
  searchModel: SearchRequestModel

  selectionTypeEnum: DocSelectionTypeEnum

  fileIds: number[]
}

export interface SortingFieldModel {
  field: string
  order: string
  keepFamilyTogether: boolean
}

export interface SearchInputParams {
  includePC?: boolean
  searchExpression: string
  isForwardFilter?: boolean
  medialist?: number[]
  filterText?: string
  isBreadCrumbClicked?: boolean
  isResetBaseGuid?: boolean
  reviewSetId?: number
  //value is set only when 'isBreadCrumbClicked' is true.
  breadCrumbIndex?: number
  folderList?: number[]
  dynamicFolderId?: number
  isDynamicFolderGlobal?: boolean
  isLoadFile?: boolean
  isSavedSearch?: boolean
  isSearchFromHistory?: boolean
  searchHistoryId?: number
  formState?: string
  isInitialSearch?: boolean
  documentShareToken?: string
  isSqlMode?: boolean
  searchQuery?: string
  projectId?: string
  searchDuplicateOption?: SearchDupOption
  navigationType?: NavigationType
  isFilterSearch?: boolean
  dynamicFolderScope?: string
  viewTagRuleConflictFiles?: ViewTagRuleConflictModel
  reviewBatchAfterCALThreshold?: boolean
}

export enum NavigationType {
  Media = 0,
  Folder = 1,
  ReviewSet = 2,
  DynamicFolder = 3,
}

export enum SearchDupOption {
  DEFAULT = -1,
  /**
   * Show only one instance in the selected scope (DynamicDeDupe™)
   */
  HIDE_ALL_DUPS_DYNAMIC = 0,
  /**
   * Show all hits in the selected scope (No DeDupe)
   */
  SHOW_ALL_DUPS = 1,
  /**
   * Show only one instance per custodian in the selected scope (DynamicDeDupe™)
   */
  HIDE_CUSTODIAN_LEVEL_DUPS_DYNAMIC = 2,
  /**
   * Hide project level duplicates (StaticDeDupe™)
   */
  HIDE_PROJECT_LEVEL_DUPS_STATIC = 3,
  /**
   * Hide custodian level duplicates (StaticDeDupe™)
   */
  HIDE_CUSTODIAN_LEVEL_DUPS_STATIC = 4,
}

export interface SearchResponseModel {
  tempTables: TempTableResponseModel

  error: ErrorMessage

  searchResultIntialParameters: InitialSearchResultParameter
}

export interface TempTableResponseModel {
  baseGUID: string

  computedSearchTempTable: string

  savedSearchTempTable: string

  searchGuid: string

  searchId: number

  searchResultTempTable: string

  userTempTable: string

  viewTypeSearchResultTempTable?: string

  viewTypePagingTempTable?: string
}

export interface ErrorMessage {
  errorMessage: string

  errorStatus: boolean
}

export interface InitialSearchResultParameter {
  currentPage?: number

  currentRecord?: number

  filteringResult?: SearchResult

  globalTempTableName?: string

  searchExpression?: string

  searchHighlightList?: string[]

  searchID?: number

  totalHitCount?: number

  fileId?: number

  pageSize?: number

  batchId?: number

  searchScopeCount?: number

  searchedDocCount?: SearchDocumentCountModel

  filteredDocCount?: SearchDocumentCountModel

  searchKeywords?: SearchTermSummary[]

  totalThreadCount?: number
}

export interface SearchResult {
  pageSize: number

  filteringResult: SearchResult

  invalidExpressions: InvalidExpression[]

  currentRecord: number

  currentPage: number

  globalTempTableName: string

  fileId: number

  searchHighlightList: string[]

  searchID: number

  searchExpression: string

  totalHitCount: number

  errorMessage: string

  errorHeader: string
}

export interface InvalidExpression {
  lineNumber: number

  searchExpression: string

  error: string
}

export interface SearchTermSummary {
  searchTerm: string
  termDetail: SearchTermDetail
}

export interface SearchTermDetail {
  documentCount: number

  looseFileCount: number

  emailCount: number

  attachmentCount: number

  embeddedCount: number
}

export interface SearchDocumentCountModel {
  totalHitCount?: number

  totalHitCountWithFamily?: number

  parentDocCount?: number

  edocCount?: number

  emailThreadCount?: number

  participantCount?: number
}

export interface FilterParams {
  fieldName: string

  filterType: string

  fieldValues: string[] | boolean[]
}

export interface BreadCrumb {
  isInitialSearch?: boolean

  query: string

  filterText?: string

  dashboardSearchResponse?: any

  docCount?: any

  itemIndex?: number

  isFilterSearch?: boolean
}

export interface ReviewBreadCrumb {
  id: string
  expression?: string
  operator: string
  isLogicalGroup: boolean
  children: ReviewBreadCrumb[]
}

export interface FilterParameterModel {
  field: string
  value: string
  operator: string
}

export interface SearchResultRequestModel {
  computedSearchTempTable: string
  guid: string
  isExternalUser: boolean
  pageNumber: number
  pageSize: string
  projectId: string
  searchResultTempTable: string
  sessionId: string
  userId: string
  totalDocuments?: number
  columnData?: string[]
  selectedFileIds?: number[]
  viewType?: ReviewViewType
  unSelectedFileIds?: number[]
  isBatchSelected?: boolean
  includeWholeThreadInExport?: boolean
}

export interface UserRolePermission {
  globalRoleId: number
  globalRoleName: string
  permission: string
}

export interface SearchHistory {
  id?: number

  includePC?: boolean

  navigationBy?: string

  navigationList?: number[]

  searchExpression?: string

  dynamicFolder?: string

  searchName?: string

  searchedBy?: string

  searchedDate?: string

  totalHits?: number

  isPrivateSearch?: string

  isLoadFileSearch?: boolean

  isSavedSearch?: boolean

  isSqlMode?: string

  searchDuplicateOption?: number

  tagGroupId?: number

  customFieldId?: number

  isDynamicFolderGlobal?: boolean
}

export interface SearchHistoryRequestModel {
  getSavedSearchOnly: boolean
  pageNumber: number
  pageSize: number
  sortFieldName: string
  sortOrder: string
}

export interface DelSearchHistoryRequestModel {
  selectedSearchHistory: number[]
  selectedSavedSearchId: number[]
  deleteSearchCustomField: boolean
  deleteSearchAutoTags: boolean
  deleteOtherSearchesWithSameExpression: boolean
}

export interface SearchStatusModel {
  projectId: number
  pageNumber: number
  selectedValues: string[]
}

export interface StatusResponseModel {
  searchId: number
  searchName: string
  completedLine: string
  completedPercentage: string
  status: string
  searchedBy: string
  ETA: string
}

export interface SearchJobCount {
  count: number
  pages: number
}

export interface SavedSearchWithTagModel {
  searchId: number
  searchName: string
  tagGroupId: number
  tagGroupName: string
}

export interface SaveSearchRequestModel {
  saveOnCustomField: boolean
  searchGuid: string
  searchName: string
  isNewCustomField?: boolean
  customFieldId?: number
  customFieldName: string
  applyAutoTagBasedOnSearchTerm?: boolean
  useExistingTag?: boolean
  tagGroupIdOfExistingSavedSearch?: number
  isBatchSelected?: boolean
  selectedFileIds?: number[]
  unSelectedFileIds?: number[]
}

export interface ViewTagRuleConflictModel {
  module: string
  conflictTagRuleIds: number[]
}
