import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentUtilityPanelComponent } from './document-utility-panel.component'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { UtilityPanelFacade } from '@venio/data-access/document-utility'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideHttpClient } from '@angular/common/http'
import { BehaviorSubject } from 'rxjs'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { VenioNotificationService } from '@venio/feature/notification'

describe('DocumentUtilityPanelComponent', () => {
  let component: DocumentUtilityPanelComponent
  let fixture: ComponentFixture<DocumentUtilityPanelComponent>
  let queryParamsSubject: BehaviorSubject<any>

  beforeEach(async () => {
    queryParamsSubject = new BehaviorSubject({ test: 'value' })
    await TestBed.configureTestingModule({
      imports: [
        DocumentUtilityPanelComponent,
        NoopAnimationsModule,
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: {} },
            queryParams: queryParamsSubject.asObservable(),
          },
        },
        UtilityPanelFacade,
        DocumentsFacade,
        StartupsFacade,
        NotificationService,
        {
          provide: VenioNotificationService,
          useValue: {
            showSuccess: jest.fn(),
            showError: jest.fn(),
            showWarning: jest.fn(),
          },
        },
        SearchFacade,
        FieldFacade,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentUtilityPanelComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
