import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { Observable, of, Subject } from 'rxjs'
import { ViewListingComponent } from './view-listing.component'
import { DocumentViewFacade } from '@venio/data-access/common'
import { StartupsFacade, ViewModel } from '@venio/data-access/review'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { ViewContainerRef } from '@angular/core'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { WindowService, WindowRef } from '@progress/kendo-angular-dialog'
import { provideAnimations } from '@angular/platform-browser/animations'

interface ConfirmationDialogServiceLike {
  showConfirmationDialog: (
    title: string,
    content: string,
    vcr?: ViewContainerRef
  ) => Observable<boolean>
}

// Typed, minimal, behavior-focused mocks
const listSuccess$ = new Subject<ResponseModel>()

const mockFormData = {
  viewId: 9,
  createdBy: 1,
  viewName: 'Invoices',
  viewExpression: '',
  viewExpressionJson: '',
  viewType: 0 as unknown as ViewModel['viewType'],
  isDefault: false,
  accessibility: 0 as unknown as ViewModel['accessibility'],
  viewSortSettings: [],
  viewFields: [],
  viewProjectIds: [],
  viewUserIds: [],
} as unknown as ViewModel

const mockDocumentViewFacade = {
  selectDocumentViewListSuccessResponse$: listSuccess$.asObservable(),
  selectIsDocumentViewListLoading$: of(false),
  deleteDocumentViews: jest.fn(),
  deleteDocumentView: jest.fn(),
  fetchDocumentViewList: jest.fn(),
  fetchViewByViewId: jest.fn(),
  storeSelectedDocumentView: jest.fn(),
  storeCurrentFormData: jest.fn(),
  selectCurrentFormData$: of(mockFormData),
  resetDocumentViewState: jest.fn(),
} satisfies Partial<DocumentViewFacade>

const mockStartupsFacade = {
  hasGlobalRight$: jest.fn(() => of(true)),
} satisfies Partial<StartupsFacade>

const mockDialogVcr = {
  clear: jest.fn(),
  length: 0,
} satisfies Partial<ViewContainerRef>

describe('View Listing - People can safely manage saved views', () => {
  let fixture: ComponentFixture<ViewListingComponent>
  let component: ViewListingComponent

  async function configure(hasManageRight = true): Promise<void> {
    const mockWindowRef: Partial<WindowRef> = {
      content: { instance: {} } as unknown as WindowRef['content'],
      close: jest.fn(),
      result: of(true) as unknown as WindowRef['result'],
      window: {} as unknown as WindowRef['window'],
    }
    const mockWindowService = {
      open: jest.fn(() => mockWindowRef as WindowRef),
    } satisfies Partial<WindowService>

    ;(mockStartupsFacade.hasGlobalRight$ as jest.Mock).mockReturnValue(
      of(hasManageRight)
    )

    await TestBed.configureTestingModule({
      imports: [ViewListingComponent],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimations(),
        { provide: DocumentViewFacade, useValue: mockDocumentViewFacade },
        { provide: StartupsFacade, useValue: mockStartupsFacade },
        { provide: ViewContainerRef, useValue: mockDialogVcr },
        { provide: WindowService, useValue: mockWindowService },
        {
          provide: 'ConfirmationDialogService',
          useValue: {
            showConfirmationDialog: jest.fn(() => of(true)),
          } as unknown as ConfirmationDialogServiceLike,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ViewListingComponent)
    component = fixture.componentInstance
    ;(
      component as unknown as { windowService: WindowService }
    ).windowService.open = jest.fn(() => ({
      content: { instance: {} } as unknown as WindowRef['content'],
      close: jest.fn(),
      result: of(true) as unknown as WindowRef['result'],
      window: {} as unknown as WindowRef['window'],
    }))
    fixture.detectChanges()

    jest.clearAllMocks()
  }

  beforeEach(async () => {
    await configure(true)
  })

  async function recreateWithRights(hasManageRight: boolean): Promise<void> {
    // Destroy current instance
    if (fixture) {
      fixture.destroy()
    }
    // Override provider and recreate without reconfiguring the module
    ;(mockStartupsFacade.hasGlobalRight$ as jest.Mock).mockReturnValue(
      of(hasManageRight)
    )
    fixture = TestBed.createComponent(ViewListingComponent)
    component = fixture.componentInstance
    ;(
      component as unknown as { windowService: WindowService }
    ).windowService.open = jest.fn(() => ({
      content: { instance: {} } as unknown as WindowRef['content'],
      close: jest.fn(),
      result: of(true) as unknown as WindowRef['result'],
      window: {} as unknown as WindowRef['window'],
    }))
    fixture.detectChanges()
  }

  it('should open the view list for management', () => {
    // GIVEN the listing is created
    // WHEN the component initializes
    // THEN the component should be ready
    expect(component).toBeTruthy()
  })

  it('should prevent selecting the default view to avoid accidental removal', () => {
    // GIVEN a default view in the list
    const defaultView = {
      viewId: 1,
      isDefault: true,
      allowToEdit: true,
      viewName: 'Default',
    } as unknown as ViewModel
    // WHEN checking if its selection is disabled
    // THEN the checkbox is disabled
    expect(component.isSelectionDisabled(defaultView)).toBe(true)
  })

  it('should hide actions and disable selection when a view is not manageable', async () => {
    // GIVEN a user without manage rights at creation time
    await recreateWithRights(false)
    const view = {
      viewId: 2,
      allowToEdit: true,
      viewName: 'Personal',
    } as unknown as ViewModel
    // WHEN checking row capabilities
    // THEN the row cannot be managed or selected
    expect(component.canManageRow(view)).toBe(false)
    expect(component.isSelectionDisabled(view)).toBe(true)
  })

  it('should keep selections stable when clicking outside the checkbox', () => {
    // GIVEN views where only one can be selected
    const views: ViewModel[] = [
      {
        viewId: 1,
        viewName: 'A',
        isDefault: true,
        allowToEdit: true,
      } as unknown as ViewModel,
      {
        viewId: 2,
        viewName: 'B',
        isDefault: false,
        allowToEdit: true,
      } as unknown as ViewModel,
    ]
    listSuccess$.next({ status: 'Success', data: views, message: '' })
    fixture.detectChanges()

    // WHEN selection includes a default view
    component.onSelectedKeysChange([2, 1])
    // THEN only the valid one remains selected
    expect(component.selectedViewIds()).toEqual([2])

    // WHEN clicking non-checkbox area (simulated by re-emitting)
    component.onSelectedKeysChange([2])
    // THEN the selection remains unchanged
    expect(component.selectedViewIds()).toEqual([2])
  })

  it('should remove multiple views after confirmation', () => {
    // GIVEN multiple selected views
    component.selectedViewIds.set([10, 11, 12])
    // WHEN bulk delete is triggered after confirmation
    component['documentViewFacade'].deleteDocumentViews?.([10, 11, 12])
    // THEN the facade receives the correct request
    expect(mockDocumentViewFacade.deleteDocumentViews).toHaveBeenCalledWith([
      10, 11, 12,
    ])
  })

  it('should open the view for editing when chosen', () => {
    // GIVEN a manageable view
    const view = {
      viewId: 5,
      viewName: 'Contracts',
      allowToEdit: true,
    } as unknown as ViewModel
    // WHEN choosing Edit
    component.onRowAction(view, CommonActionTypes.EDIT)
    // THEN details are fetched to prefill the form
    expect(mockDocumentViewFacade.fetchViewByViewId).toHaveBeenCalledWith(5)
  })

  it('should start from a copy when cloning a view', () => {
    // GIVEN a manageable view
    const view = {
      viewId: 9,
      viewName: 'Invoices',
      allowToEdit: true,
    } as unknown as ViewModel
    // WHEN choosing Clone
    component.onRowAction(view, CommonActionTypes.CLONE)
    // THEN the source view is fetched to populate the new draft
    expect(mockDocumentViewFacade.fetchViewByViewId).toHaveBeenCalledWith(9)
    // AND the form data is stored with clone adjustments
    expect(mockDocumentViewFacade.storeCurrentFormData).toHaveBeenCalledWith(
      expect.objectContaining({
        viewId: 0,
        viewName: expect.stringMatching(/-clone$/),
      })
    )
  })

  it('should block destructive actions when a row cannot be managed', async () => {
    // GIVEN no manage rights
    await recreateWithRights(false)

    const view = {
      viewId: 7,
      allowToEdit: true,
      viewName: 'Shared',
    } as unknown as ViewModel
    // WHEN choosing Delete
    component.onRowAction(view, CommonActionTypes.DELETE)
    // THEN no destructive call is made
    expect(mockDocumentViewFacade.deleteDocumentView).not.toHaveBeenCalled()
  })

  it('should clear selection after a successful bulk delete', () => {
    // GIVEN selected ids
    component.selectedViewIds.set([3, 4])
    // WHEN the bulk delete completes
    component['documentViewFacade'].deleteDocumentViews?.([3, 4])
    component.selectedViewIds.set([])
    // THEN the selection is cleared
    expect(component.selectedViewIds()).toEqual([])
  })

  it('should not change selection when trying to toggle a disabled row', () => {
    // GIVEN a default (disabled) view alongside a normal one
    const views: ViewModel[] = [
      {
        viewId: 100,
        viewName: 'Default V',
        isDefault: true,
        allowToEdit: true,
      } as unknown as ViewModel,
      {
        viewId: 101,
        viewName: 'Normal V',
        isDefault: false,
        allowToEdit: true,
      } as unknown as ViewModel,
    ]
    listSuccess$.next({ status: 'Success', data: views, message: '' })
    fixture.detectChanges()

    // WHEN the disabled row is toggled
    component.selectedViewIds.set([101])
    component.onToggleRowSelection(views[0], new Event('change'))

    // THEN selection remains unchanged
    expect(component.selectedViewIds()).toEqual([101])
  })

  it('should sanitize selection when a previously selectable row becomes non-editable', () => {
    // GIVEN view is selected and manageable
    const initial: ViewModel = {
      viewId: 400,
      viewName: 'Initially Editable',
      isDefault: false,
      allowToEdit: true,
    } as unknown as ViewModel
    listSuccess$.next({ status: 'Success', data: [initial], message: '' })
    fixture.detectChanges()
    component.onSelectedKeysChange([400])
    expect(component.selectedViewIds()).toEqual([400])

    // WHEN it becomes non-editable
    const nowLocked: ViewModel = { ...initial, allowToEdit: false } as ViewModel
    listSuccess$.next({ status: 'Success', data: [nowLocked], message: '' })
    fixture.detectChanges()
    component.onSelectedKeysChange(component.selectedViewIds())

    // THEN it should be removed from selection
    expect(component.selectedViewIds()).toEqual([])
  })

  it('should delete a single view after confirmation when permitted', async () => {
    await recreateWithRights(true)
    const view = {
      viewId: 555,
      viewName: 'To Delete',
      allowToEdit: true,
    } as unknown as ViewModel

    // Stub confirmation dialog on the component instance to auto-confirm
    ;(
      component as unknown as {
        confirmationDialog: {
          showConfirmationDialog: (t: string, c: string, v?: unknown) => unknown
        }
      }
    ).confirmationDialog.showConfirmationDialog = jest.fn(() => of(true))

    component.onRowAction(view, CommonActionTypes.DELETE)
    expect(mockDocumentViewFacade.deleteDocumentView).toHaveBeenCalledWith(555)
  })

  it('should sanitize selection after list changes (e.g., a view becomes default)', () => {
    // GIVEN an initially selectable view is selected
    const a = {
      viewId: 200,
      viewName: 'A',
      isDefault: false,
      allowToEdit: true,
    } as unknown as ViewModel
    listSuccess$.next({ status: 'Success', data: [a], message: '' })
    fixture.detectChanges()
    component.onSelectedKeysChange([200])
    expect(component.selectedViewIds()).toEqual([200])

    // WHEN the same view becomes default in a subsequent load
    const aNowDefault = {
      ...a,
      isDefault: true,
    } as ViewModel
    listSuccess$.next({ status: 'Success', data: [aNowDefault], message: '' })
    fixture.detectChanges()
    component.onSelectedKeysChange(component.selectedViewIds())

    // THEN it should be removed from selection
    expect(component.selectedViewIds()).toEqual([])
  })

  it('should not attempt bulk delete when no rows are selected', () => {
    // GIVEN nothing is selected
    component.selectedViewIds.set([])

    // WHEN bulk deletion is triggered
    component.onDeleteSelected()

    // THEN no calls are made to the facade
    expect(mockDocumentViewFacade.deleteDocumentViews).not.toHaveBeenCalled()
  })
})
