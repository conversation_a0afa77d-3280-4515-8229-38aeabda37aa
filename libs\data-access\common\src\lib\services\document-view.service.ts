import { Injectable } from '@angular/core'
import { HttpClient } from '@angular/common/http'
import { environment } from '@venio/shared/environments'
import { Observable } from 'rxjs'
import {
  ResponseModel,
  SearchFieldModel,
} from '@venio/shared/models/interfaces'
import { ViewModel } from '@venio/data-access/review'

@Injectable({ providedIn: 'root' })
export class DocumentViewService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public fetchDocumentList(projectId: number): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(
      `${this._apiUrl}project/${projectId}/Views`
    )
  }

  public fetchSearchFieldList(projectId: number): Observable<SearchFieldModel> {
    return this.http.get<SearchFieldModel>(
      `${this._apiUrl}/SearchField/view-condition`
    )
  }

  public fetchViewByViewId(viewId: number): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(`${this._apiUrl}View/${viewId}`)
  }

  public addOrUpdateView(payload: ViewModel): Observable<ResponseModel> {
    // post if payload.viewId is null, put if payload.viewId is not null
    const isUpdate = payload.viewId > 0
    const url = isUpdate
      ? `${this._apiUrl}View/${payload.viewId}`
      : `${this._apiUrl}View`
    return isUpdate
      ? this.http.put<ResponseModel>(url, payload)
      : this.http.post<ResponseModel>(url, payload)
  }

  /**
   * Deletes a view by id
   * @param {number} viewId
   * @returns {Observable<ResponseModel>}
   */
  public deleteDocumentView(viewId: number): Observable<ResponseModel> {
    // Use the bulk endpoint with a single id to keep behavior consistent
    return this.deleteDocumentViews([viewId])
  }

  /**
   * Bulk deletes views by ids
   * @param {number[]} viewIds
   * @returns {Observable<ResponseModel>}
   */
  public deleteDocumentViews(viewIds: number[]): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(`${this._apiUrl}View/delete`, viewIds)
  }
}
