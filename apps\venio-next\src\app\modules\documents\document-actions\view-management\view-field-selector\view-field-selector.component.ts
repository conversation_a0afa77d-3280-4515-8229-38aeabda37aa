import {
  Component,
  signal,
  computed,
  On<PERSON><PERSON><PERSON>,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  ViewChild,
  effect,
  HostListener,
  input,
  output,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { isEqual } from 'lodash'
import { toSignal } from '@angular/core/rxjs-interop'
import { CheckBoxModule, InputsModule } from '@progress/kendo-angular-inputs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DropDownsModule,
  MultiSelectTreeComponent,
} from '@progress/kendo-angular-dropdowns'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import {
  GridModule,
  GridDataResult,
  PageChangeEvent,
  GridItem,
} from '@progress/kendo-angular-grid'
import {
  Field,
  FieldFacade,
  ViewField,
  ViewModel,
  ProjectInfo,
} from '@venio/data-access/review'
import { DocumentViewFacade } from '@venio/data-access/common'
import { FieldManagerModel } from '@venio/util/utilities'
import { CommonActionTypes } from '@venio/shared/models/constants'

interface FieldItem {
  fieldId: number
  fieldName: string
  displayName: string
  isSelected: boolean
  isCustomField: boolean
  category?: string
}

// Enhanced field model with project tracking
interface ProjectField extends Field {
  projectId?: number
  sourceProjects: number[] // Track which projects this field belongs to
}

// Enhanced field manager model with project tracking
interface EnhancedFieldManagerModel extends FieldManagerModel {
  projectId?: number
  sourceProjects: number[]
}

// Grid row reorder event shape used by the right-grid reorder handler
type GridRowReorderEvent = {
  draggedRows: Array<{ dataItem: EnhancedFieldManagerModel; rowIndex: number }>
  dropTargetRow?: { dataItem: EnhancedFieldManagerModel; rowIndex: number }
  dropPosition: 'before' | 'after' | 'forbidden'
}

@Component({
  selector: 'venio-view-field-selector',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    InputsModule,
    ButtonsModule,
    DropDownsModule,
    IndicatorsModule,
    TooltipsModule,
    GridModule,
    CheckBoxModule,
  ],
  templateUrl: './view-field-selector.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ViewFieldSelectorComponent implements OnDestroy {
  private readonly changeDetectorRef = inject(ChangeDetectorRef)

  private readonly documentViewFacade = inject(DocumentViewFacade)

  private readonly fieldFacade = inject(FieldFacade)

  public selectedFields = input<ViewField[]>([])

  public readonly fieldSelectionChanged = output<ViewField[]>()

  // Unified field list supplied by container (system + custom per selected projects)
  public baseFields = input<Field[]>([])

  // Fields tab case multi-select inputs/outputs
  public availableCases = input<ProjectInfo[]>([])

  public selectedProjectIds = input<number[]>([])

  public readonly selectedProjectIdsChange = output<number[]>()

  // Track filter text for Fields tab case dropdown to support filtered Check all
  private fieldsTabCaseFilterTerm = ''

  // Enhanced signals for project-based field management
  public readonly projectFields = signal<ProjectField[]>([])

  public readonly availableFieldsForSelectedProjects = computed(() => {
    const fields = this.projectFields()
    const selectedProjectIds = this.selectedProjectIds() || []

    // When no projects are selected, show ALL fields (system + custom)
    // This ensures fields are visible for initial selection
    if (selectedProjectIds.length === 0) {
      return fields
    }

    // When projects are selected, filter appropriately
    const filtered = fields.filter((field) => {
      // System fields (no projectId) are always available
      if (!field.projectId) {
        return true
      }

      // Custom fields must belong to at least one selected project
      return field.sourceProjects.some((projectId) =>
        selectedProjectIds.includes(projectId)
      )
    })

    return filtered
  })

  // Left fields (available for selection) - now using GridDataResult
  public readonly leftFields = signal<GridDataResult>({ data: [], total: 0 })

  // Right fields (selected and reorderable) - now using GridDataResult
  public readonly rightFields = signal<GridDataResult>({ data: [], total: 0 })

  // Store the full arrays for data management
  private readonly allLeftFields = signal<EnhancedFieldManagerModel[]>([])

  private readonly allRightFields = signal<EnhancedFieldManagerModel[]>([])

  public readonly selectedRightFields = signal<EnhancedFieldManagerModel[]>([])

  public readonly selectedLeftFields = signal<EnhancedFieldManagerModel[]>([])

  // Virtualization properties - each grid needs its own state
  public leftPageSize = 70

  public leftSkip = 0

  public rightPageSize = 70

  public rightSkip = 0

  // Loading states
  public isLoading = signal(false)

  // Track action type for loading indicator
  private lastViewFieldsSnapshot: ViewField[] | null = null

  // Legacy computed for backward compatibility with existing template
  public allFields = computed<FieldItem[]>(() => {
    const left = this.leftFields()?.data ?? []
    const right = this.rightFields()?.data ?? []
    return [
      ...left.map((field) => ({
        fieldId: field.venioFieldId,
        fieldName: field.displayFieldName,
        displayName: field.displayFieldName,
        isSelected: false,
        isCustomField: field.isCustomField,
        category: field.isCustomField ? 'Custom' : 'Standard',
      })),
      ...right.map((field) => ({
        fieldId: field.venioFieldId,
        fieldName: field.displayFieldName,
        displayName: field.displayFieldName,
        isSelected: true,
        isCustomField: field.isCustomField,
        category: field.isCustomField ? 'Custom' : 'Standard',
      })),
    ]
  })

  public filteredFields = computed<FieldItem[]>(() => {
    const all = this.allFields() || []
    const search = (this.searchTerm() || '').toLowerCase()
    const category = this.selectedCategory()
    let filtered = all
    if (search) {
      filtered = filtered.filter(
        (field) =>
          field.displayName.toLowerCase().includes(search) ||
          field.fieldName.toLowerCase().includes(search)
      )
    }
    switch (category) {
      case 'Standard Fields':
        filtered = filtered.filter((field) => !field.isCustomField)
        break
      case 'Custom Fields':
        filtered = filtered.filter((field) => field.isCustomField)
        break
      case 'Selected Cases':
      case 'All Fields':
      default:
        break
    }
    return filtered
  })

  public searchTerm = signal('')

  public selectedCategory = signal('Selected Cases')

  // Categories for dropdown
  public categories = [
    'Selected Cases',
    'All Fields',
    'Standard Fields',
    'Custom Fields',
  ]

  // Computed properties for backward compatibility
  public unselectedFields = computed(() => {
    return this.filteredFields().filter((field) => !field.isSelected)
  })

  public selectedFieldsList = computed(() => {
    return this.filteredFields().filter((field) => field.isSelected)
  })

  // Keys for left grid selection
  public leftSelectedKeys: string[] = []

  @ViewChild('fieldsTabCaseTree')
  public fieldsTabCaseTree: MultiSelectTreeComponent

  /**
   * Close the Fields tab case dropdown when clicking outside (toolbar parity)
   * @param {MouseEvent} event - The document click event
   * @returns {void}
   */
  @HostListener('document:click', ['$event'])
  public handleDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement
    const isInsidePopup =
      target?.closest?.('.custom-multiselecttree-popup') !== null
    const isInsideWrapper =
      this.fieldsTabCaseTree?.wrapper?.nativeElement?.contains(target)
    if (isInsidePopup || isInsideWrapper) return
    if (this.fieldsTabCaseTree?.isOpen) this.fieldsTabCaseTree.toggle(false)
  }

  constructor() {
    // React to supplied base fields (runs in injection context)
    effect(
      () => {
        const fields = this.baseFields() || []
        if (!fields || fields.length === 0) return
        this.#processBaseFields(fields)
        this.#recalculateModifiedFields()
      },
      { allowSignalWrites: true }
    )

    // React to selected project changes
    effect(
      () => {
        const selectedProjectIds = this.selectedProjectIds() || []
        this.#handleProjectSelectionChange(selectedProjectIds)
      },
      { allowSignalWrites: true }
    )

    // React to form data updates (runs in injection context)
    effect(
      () => {
        const formData = this.currentFormData()
        const viewFields = formData?.viewFields
        if (!viewFields) return
        if (isEqual(this.lastViewFieldsSnapshot, viewFields)) return
        this.lastViewFieldsSnapshot = Array.isArray(viewFields)
          ? [...viewFields]
          : viewFields
        this.#setSavedFormData(formData)
        this.#storeUpdatesOfSelectedFields()
      },
      { allowSignalWrites: true }
    )
  }

  public ngOnDestroy(): void {
    // Clear signals to help with garbage collection
    this.leftFields.set({ data: [], total: 0 })
    this.rightFields.set({ data: [], total: 0 })
    this.allLeftFields.set([])
    this.allRightFields.set([])
    this.selectedLeftFields.set([])
    this.selectedRightFields.set([])
  }

  /**
   * Process base fields and add project tracking
   * @param {Field[]} fields - Array of base fields to process
   * @returns {void}
   */
  #processBaseFields(fields: Field[]): void {
    const projectFields: ProjectField[] = fields.map((field) => {
      const extendedField = field as ProjectField
      return {
        ...field,
        projectId: extendedField.projectId, // Custom fields have projectId
        sourceProjects: extendedField.projectId
          ? [extendedField.projectId]
          : [], // System fields have no project
      }
    })

    this.projectFields.set(projectFields)

    // Initialize the left fields with all available fields (system + custom)
    // This ensures fields are visible even when no projects are selected
    const initialLeftFields = this.#mapBaseArray(projectFields)
    // Reset pagination to avoid out-of-range pages after data changes
    this.leftSkip = 0
    this.rightSkip = 0
    this.#updateLeftFieldsData(initialLeftFields)

    // Update change detection
    this.changeDetectorRef.markForCheck()
  }

  /**
   * Update left fields data with proper GridDataResult format
   * @param {EnhancedFieldManagerModel[]} fields - Array of fields to set
   * @returns {void}
   */
  #updateLeftFieldsData(fields: EnhancedFieldManagerModel[]): void {
    // Store the full array
    this.allLeftFields.set(fields)

    const startIndex = this.leftSkip
    const endIndex = startIndex + this.leftPageSize
    const data = fields.slice(startIndex, endIndex)

    this.leftFields.set({
      data,
      total: fields.length,
    })
  }

  /**
   * Update right fields data with proper GridDataResult format
   * @param {EnhancedFieldManagerModel[]} fields - Array of fields to set
   * @returns {void}
   */
  #updateRightFieldsData(fields: EnhancedFieldManagerModel[]): void {
    // Store the full array
    this.allRightFields.set(fields)

    const startIndex = this.rightSkip
    const endIndex = startIndex + this.rightPageSize
    const data = fields.slice(startIndex, endIndex)

    this.rightFields.set({
      data,
      total: fields.length,
    })
  }

  /**
   * Handle page change for virtualization
   * @param {PageChangeEvent} event - Page change event
   * @returns {void}
   */
  public onLeftPageChange(event: PageChangeEvent): void {
    this.leftSkip = event.skip
    this.#updateLeftFieldsData(this.#getAllLeftFields())
  }

  /**
   * Handle page change for virtualization
   * @param {PageChangeEvent} event - Page change event
   * @returns {void}
   */
  public onRightPageChange(event: PageChangeEvent): void {
    this.rightSkip = event.skip
    this.#updateRightFieldsData(this.#getAllRightFields())
  }

  /**
   * Get all left fields (not just the current page)
   * @returns {EnhancedFieldManagerModel[]} All left fields
   */
  #getAllLeftFields(): EnhancedFieldManagerModel[] {
    return this.allLeftFields()
  }

  /**
   * Get all right fields (not just the current page)
   * @returns {EnhancedFieldManagerModel[]} All right fields
   */
  #getAllRightFields(): EnhancedFieldManagerModel[] {
    return this.allRightFields()
  }

  /**
   * Handle project selection changes
   * @param {number[]} selectedProjectIds - Array of selected project IDs
   * @returns {void}
   */
  #handleProjectSelectionChange(selectedProjectIds: number[]): void {
    // Remove fields that no longer belong to any selected project
    const currentRightFields = this.#getAllRightFields()
    const availableFieldNames = new Set(
      this.availableFieldsForSelectedProjects().map((f) => f.displayFieldName)
    )

    // Preserve fields explicitly returned by the edit payload (viewFields),
    // so they remain visible even if custom field metadata loads later.
    const savedSnapshot = this.lastViewFieldsSnapshot || []
    const savedNames = new Set(savedSnapshot.map((v) => v.fieldName))

    const filteredRightFields = currentRightFields.filter((field) => {
      return (
        availableFieldNames.has(field.displayFieldName) ||
        savedNames.has(field.displayFieldName)
      )
    })

    if (filteredRightFields.length !== currentRightFields.length) {
      // Reset pagination when the total changes significantly
      this.rightSkip = 0
      this.#updateRightFieldsData(filteredRightFields)
      this.#storeUpdatesOfSelectedFields()
    }

    this.changeDetectorRef.markForCheck()
  }

  /**
   * Fields tab case dropdown filter change handler
   * @param {string} term - The filter term to apply
   * @returns {void}
   */
  public onFieldsTabCaseFilterChange(term: string): void {
    this.fieldsTabCaseFilterTerm = (term || '').trim().toLowerCase()
  }

  /**
   * Returns filtered cases based on current header filter
   * @returns {ProjectInfo[]} Array of filtered project info objects
   */
  private getFilteredCases(): ProjectInfo[] {
    const term = this.fieldsTabCaseFilterTerm
    const source = this.availableCases() || []
    if (!term) return source
    return source.filter((p) =>
      (p.projectName || '').toLowerCase().includes(term)
    )
  }

  /**
   * Reflects toolbar parity: true when all filtered cases are selected
   * @returns {boolean} True if all filtered cases are selected
   */
  public isAllCheckedForFieldsTab(): boolean {
    const selected = this.selectedProjectsAsItems
    const filtered = this.getFilteredCases()
    return (
      Array.isArray(selected) &&
      selected.length > 0 &&
      selected.length === filtered.length
    )
  }

  /**
   * Reflects toolbar parity: true when partially selected among filtered
   * @returns {boolean} True if some but not all filtered cases are selected
   */
  public isIndeterminateForFieldsTab(): boolean {
    const selected = this.selectedProjectsAsItems
    const filtered = this.getFilteredCases()
    return (
      Array.isArray(selected) &&
      selected.length > 0 &&
      selected.length < filtered.length
    )
  }

  /**
   * Current selected projects as items for multiselect binding
   * @returns {ProjectInfo[]} Array of selected project info objects
   */
  public get selectedProjectsAsItems(): ProjectInfo[] {
    const ids = new Set(this.selectedProjectIds() || [])
    return (this.availableCases() || []).filter((c) => ids.has(c.projectId))
  }

  /**
   * Handle fields tab case selection changes
   * @param {ProjectInfo[]} items - Array of selected project items
   * @returns {void}
   */
  public onFieldsTabCasesChange(items: ProjectInfo[]): void {
    const ids = (items || []).map((c) => c.projectId)
    this.selectedProjectIdsChange.emit(ids)
  }

  /**
   * Handle Select All checkbox in case multiselect header
   * @param {unknown} event - The checkbox change event
   * @returns {void}
   */
  public onSelectAllChange(event: unknown): void {
    const checked = this.#getCheckboxChecked(event)
    const filtered = this.getFilteredCases()
    const allIds = filtered.map((c) => c.projectId)
    this.onFieldsTabIdsChange(checked ? allIds : [])
  }

  /**
   * Handle primitive valueChange from MultiSelect when valuePrimitive=true
   * @param {number[]} projectIds - Array of selected project IDs
   * @returns {void}
   */
  public onFieldsTabIdsChange(projectIds: number[]): void {
    this.selectedProjectIdsChange.emit(projectIds || [])
  }

  /**
   * Safely extract checked state from checkbox-like events
   * @param {unknown} event - The checkbox event object
   * @returns {boolean} - True if checkbox is checked, false otherwise
   */
  #getCheckboxChecked(event: unknown): boolean {
    const maybeTarget = (event as { target?: { checked?: boolean } })?.target
    return maybeTarget?.checked === true
  }

  private readonly currentFormData = toSignal(
    this.documentViewFacade.selectCurrentFormData$,
    { initialValue: null as unknown as ViewModel }
  )

  /**
   * Initialize with real data handled by signal effects in ngOnInit
   * @param {ProjectField[]} fields - Array of project fields to map
   * @returns {EnhancedFieldManagerModel[]} Array of enhanced field manager models
   */
  #mapBaseArray(fields: ProjectField[]): EnhancedFieldManagerModel[] {
    return fields.map(
      (c) =>
        ({
          displayFieldName: c.displayFieldName,
          venioFieldId: c.venioFieldId,
          isCustomField: c.isCustomField,
          fieldDisplayOrder: c.displayOrder || 0,
          projectId: c.projectId,
          sourceProjects: c.sourceProjects || [],
        } as EnhancedFieldManagerModel)
    )
  }

  #mapViewFields(
    baseFields: ProjectField[],
    viewFields: ViewField[]
  ): EnhancedFieldManagerModel[] {
    if (!viewFields?.length) return []

    return viewFields.map((c) => {
      const field = baseFields.find((d) => d.displayFieldName === c.fieldName)
      // Fallback so saved custom fields render on the right grid even
      // before their base/custom metadata is loaded (e.g., before case selection)
      return {
        displayFieldName: field?.displayFieldName ?? c.fieldName,
        venioFieldId: field?.venioFieldId ?? c.fieldId,
        isCustomField: field?.isCustomField ?? c.isCustomField ?? false,
        fieldDisplayOrder: field?.displayOrder || 0,
        projectId: field?.projectId,
        sourceProjects: field?.sourceProjects || [],
      } as EnhancedFieldManagerModel
    })
  }

  #recalculateModifiedFields(shouldResetSelected = false): void {
    this.changeDetectorRef.markForCheck()

    if (!shouldResetSelected) return

    this.selectedRightFields.set([])
    this.selectedLeftFields.set([])
    this.leftSelectedKeys = []
  }

  #setSavedFormData(viewModel: ViewModel): void {
    const savedFieldFormData = this.#mapViewFields(
      this.projectFields() || [],
      viewModel?.viewFields
    )
    this.#updateRightFieldsData(savedFieldFormData)
    // reflect selection on left grid across ALL selected items
    this.leftSelectedKeys = this.#getAllRightFields().map(
      (f: EnhancedFieldManagerModel) => f.displayFieldName
    )
    this.changeDetectorRef.markForCheck()
  }

  #storeUpdatesOfSelectedFields(): void {
    const selectedFields = this.#getAllRightFields()

    const viewFields: ViewField[] = selectedFields.map((c, index) => ({
      fieldId: c.venioFieldId,
      fieldName: c.displayFieldName,
      isCustomField: c.isCustomField,
      fieldOrder: index + 1,
    }))

    // Emit to parent component
    this.fieldSelectionChanged.emit(viewFields)

    // Store in facade for persistence
    this.documentViewFacade.storeCurrentFormData({
      viewFields,
    })
  }

  /**
   * Grid selection on left: move checked to right, uncheck removes from right
   * @param {unknown} event - Selection event from Kendo Grid
   * @returns {void}
   */
  public onSelectionChangeAvailableFields(event: unknown): void {
    const selectionEvent = event as {
      selectedRows?: Array<{ dataItem: EnhancedFieldManagerModel }>
      deselectedRows?: Array<{ dataItem: EnhancedFieldManagerModel }>
    }

    const selectedItems: Array<EnhancedFieldManagerModel> =
      selectionEvent.selectedRows?.map(
        (row: { dataItem: EnhancedFieldManagerModel }) => row.dataItem
      ) || []
    const deselectedItems: Array<EnhancedFieldManagerModel> =
      selectionEvent.deselectedRows?.map(
        (row: { dataItem: EnhancedFieldManagerModel }) => row.dataItem
      ) || []

    // Add newly selected to right (unique)
    if (selectedItems.length) {
      const existing = new Set(
        this.#getAllRightFields().map((f) => f.displayFieldName)
      )
      const additions = selectedItems.filter(
        (f) => !existing.has(f.displayFieldName)
      )
      if (additions.length) {
        const currentRight = this.#getAllRightFields()
        this.#updateRightFieldsData([...currentRight, ...additions])
      }
    }

    // Remove deselected from right
    if (deselectedItems.length) {
      const removed = new Set(deselectedItems.map((f) => f.displayFieldName))
      const filtered = this.#getAllRightFields().filter(
        (f) => !removed.has(f.displayFieldName)
      )
      this.#updateRightFieldsData(filtered)
    }

    // Reflect selection state on left
    this.leftSelectedKeys = this.#getAllRightFields().map(
      (f) => f.displayFieldName
    )

    this.#storeUpdatesOfSelectedFields()
    this.changeDetectorRef.markForCheck()
  }

  /**
   * Handle row reorder on the right grid by updating the underlying full array order.
   * Uses row reorder info carrying dragged rows, target row and insertion position.
   * @param {GridRowReorderEvent} event - Kendo grid row reorder event
   * @returns {void}
   */
  public onRightGridReorder(event: GridRowReorderEvent): void {
    const dragged = event?.draggedRows
    const dropTarget = event?.dropTargetRow
    const dropPosition = event?.dropPosition

    if (!Array.isArray(dragged) || dragged.length === 0) return
    if (!dropTarget || dropPosition === 'forbidden') return

    const all = [...this.#getAllRightFields()]
    const key = (f: EnhancedFieldManagerModel): string => f.displayFieldName

    // Determine dragged order based on current array order to preserve relative ordering
    const draggedKeys = dragged.map((d) => key(d.dataItem)).filter(Boolean)
    const draggedSet = new Set(draggedKeys)
    const draggedInOrder = all.filter((f) => draggedSet.has(key(f)))

    // Remove dragged items from the array
    const remainder = all.filter((f) => !draggedSet.has(key(f)))

    if (!dropTarget.dataItem) return

    // Compute insertion index relative to remainder
    const targetKey = key(dropTarget.dataItem)
    let insertIndex = remainder.findIndex((f) => key(f) === targetKey)
    if (insertIndex < 0) insertIndex = remainder.length
    if (dropPosition === 'after') insertIndex += 1

    // Rebuild array with dragged items inserted
    const reordered = [
      ...remainder.slice(0, insertIndex),
      ...draggedInOrder,
      ...remainder.slice(insertIndex),
    ]

    this.#updateRightFieldsData(reordered)

    // reflect selection and persist
    this.leftSelectedKeys = this.#getAllRightFields().map(
      (f) => f.displayFieldName
    )
    this.#storeUpdatesOfSelectedFields()
    this.changeDetectorRef.markForCheck()
  }

  public onSearchChange(searchTerm: string): void {
    this.searchTerm.set(searchTerm)
  }

  public onCategoryChange(category: string): void {
    this.selectedCategory.set(category)
  }

  /**
   * Toggle field selection between left and right grids
   * @param {FieldItem} field - The field item to toggle
   * @returns {void}
   */
  public toggleFieldSelection(field: FieldItem): void {
    const leftFieldIndex = this.#getAllLeftFields().findIndex(
      (f) => f.displayFieldName === field.fieldName
    )
    const rightFieldIndex = this.#getAllRightFields().findIndex(
      (f) => f.displayFieldName === field.fieldName
    )

    if (leftFieldIndex >= 0) {
      const fieldToMove = this.#getAllLeftFields()[leftFieldIndex]
      const currentRight = this.#getAllRightFields()
      this.#updateRightFieldsData([...currentRight, fieldToMove])
    } else if (rightFieldIndex >= 0) {
      const fieldToMove = this.#getAllRightFields()[rightFieldIndex]
      const filtered = this.#getAllRightFields().filter(
        (f) => f !== fieldToMove
      )
      this.#updateRightFieldsData(filtered)
    }

    this.leftSelectedKeys = this.#getAllRightFields().map(
      (f) => f.displayFieldName
    )
    this.#storeUpdatesOfSelectedFields()
  }

  /**
   * Toggle select-all for left list
   * @param {boolean} checked - New checked state for the header checkbox
   * @returns {void}
   */
  public onLeftHeaderToggle(checked: boolean): void {
    if (checked) {
      this.selectAllUnselected()
    } else {
      this.deselectAllSelected()
    }
    // Keep the view consistent after bulk actions
    this.leftSkip = 0
    this.rightSkip = 0
    this.#updateLeftFieldsData(this.#getAllLeftFields())
    this.#updateRightFieldsData(this.#getAllRightFields())
    this.changeDetectorRef.markForCheck()
  }

  public selectAllUnselected(): void {
    // Select all left items to right
    const existing = new Set(
      this.#getAllRightFields().map((f) => f.displayFieldName)
    )
    const additions = this.#getAllLeftFields().filter(
      (f) => !existing.has(f.displayFieldName)
    )
    const currentRight = this.#getAllRightFields()
    this.rightSkip = 0
    this.#updateRightFieldsData([...currentRight, ...additions])
    this.leftSelectedKeys = this.#getAllRightFields().map(
      (f) => f.displayFieldName
    )
    this.#storeUpdatesOfSelectedFields()
  }

  public deselectAllSelected(): void {
    this.rightSkip = 0
    this.#updateRightFieldsData([])
    this.leftSelectedKeys = []
    this.#storeUpdatesOfSelectedFields()
  }

  public selectionItemChanged(
    items: EnhancedFieldManagerModel[],
    isRight = false
  ): void {
    this.changeDetectorRef.markForCheck()
    if (isRight) {
      this.selectedLeftFields.set([])
      this.selectedRightFields.set(items)
    } else {
      this.selectedRightFields.set([])
      this.selectedLeftFields.set(items)
    }
  }

  public onUnselectedFieldClick(field: FieldItem): void {
    this.toggleFieldSelection(field)
  }

  public onSelectedFieldClick(field: FieldItem): void {
    this.toggleFieldSelection(field)
  }

  public getFieldDisplayName(field: FieldItem): string {
    return field.displayName || field.fieldName
  }

  public getFieldNumber(index: number): number {
    return index + 1
  }

  /**
   * Sync selectedRightFields back into store
   * @param {CommonActionTypes} commonActionTypes - The action type to perform
   * @param {boolean} isAll - Whether to apply to all items
   * @returns {void}
   */
  public fieldSelectionActionClick(
    commonActionTypes: CommonActionTypes,
    isAll = false
  ): void {
    switch (commonActionTypes) {
      case CommonActionTypes.RIGHT:
        if (isAll) {
          this.#updateRightFieldsData(this.#getAllLeftFields())
        } else {
          const currentRight = this.#getAllRightFields()
          this.#updateRightFieldsData([
            ...currentRight,
            ...this.selectedLeftFields(),
          ])
        }
        break
      case CommonActionTypes.LEFT:
        if (isAll) {
          this.#updateRightFieldsData([])
        } else {
          const removeSet = new Set(
            this.selectedRightFields().map((f) => f.displayFieldName)
          )
          const filtered = this.#getAllRightFields().filter(
            (f) => !removeSet.has(f.displayFieldName)
          )
          this.#updateRightFieldsData(filtered)
        }
        break
      case CommonActionTypes.SWAP:
        this.#updateRightFieldsData(this.#getAllLeftFields())
        break
    }
    this.leftSelectedKeys = this.#getAllRightFields().map(
      (f) => f.displayFieldName
    )
    this.#storeUpdatesOfSelectedFields()
  }

  /**
   * Handle field reordering using direct array manipulation
   * @param {CommonActionTypes} commonActionTypes - The reorder action type
   * @returns {void}
   */
  public fieldReorder(commonActionTypes: CommonActionTypes): void {
    const currentRightFields = [...this.#getAllRightFields()]
    const selectedFields = this.selectedRightFields()

    if (!selectedFields.length) return

    let shouldResetSelected = false

    switch (commonActionTypes) {
      case CommonActionTypes.TOP:
        this.#moveFieldsToTop(currentRightFields, selectedFields)
        shouldResetSelected = true
        break
      case CommonActionTypes.UP:
        this.#moveFieldsUp(currentRightFields, selectedFields)
        break
      case CommonActionTypes.DOWN:
        this.#moveFieldsDown(currentRightFields, selectedFields)
        break
      case CommonActionTypes.BOTTOM:
        this.#moveFieldsToBottom(currentRightFields, selectedFields)
        shouldResetSelected = true
        break
    }

    // Update the right fields with the new order
    this.#updateRightFieldsData(currentRightFields)

    if (shouldResetSelected) {
      this.#recalculateModifiedFields(true)
    }

    this.#storeUpdatesOfSelectedFields()
  }

  /**
   * Move selected fields to the top of the array
   * @param {EnhancedFieldManagerModel[]} fields - The fields array to modify
   * @param {EnhancedFieldManagerModel[]} selectedFields - The fields to move
   * @returns {void}
   */
  #moveFieldsToTop(
    fields: EnhancedFieldManagerModel[],
    selectedFields: EnhancedFieldManagerModel[]
  ): void {
    const selectedNames = new Set(selectedFields.map((f) => f.displayFieldName))
    const selected = fields.filter((f) => selectedNames.has(f.displayFieldName))
    const unselected = fields.filter(
      (f) => !selectedNames.has(f.displayFieldName)
    )

    // Clear and rebuild array with selected first, then unselected
    fields.length = 0
    fields.push(...selected, ...unselected)
  }

  /**
   * Move selected fields up by one position
   * @param {EnhancedFieldManagerModel[]} fields - The fields array to modify
   * @param {EnhancedFieldManagerModel[]} selectedFields - The fields to move
   * @returns {void}
   */
  #moveFieldsUp(
    fields: EnhancedFieldManagerModel[],
    selectedFields: EnhancedFieldManagerModel[]
  ): void {
    const selectedNames = new Set(selectedFields.map((f) => f.displayFieldName))

    for (let i = 1; i < fields.length; i++) {
      if (selectedNames.has(fields[i].displayFieldName)) {
        // Swap with previous element
        ;[fields[i], fields[i - 1]] = [fields[i - 1], fields[i]]
      }
    }
  }

  /**
   * Move selected fields down by one position
   * @param {EnhancedFieldManagerModel[]} fields - The fields array to modify
   * @param {EnhancedFieldManagerModel[]} selectedFields - The fields to move
   * @returns {void}
   */
  #moveFieldsDown(
    fields: EnhancedFieldManagerModel[],
    selectedFields: EnhancedFieldManagerModel[]
  ): void {
    const selectedNames = new Set(selectedFields.map((f) => f.displayFieldName))

    for (let i = fields.length - 2; i >= 0; i--) {
      if (selectedNames.has(fields[i].displayFieldName)) {
        // Swap with next element
        ;[fields[i], fields[i + 1]] = [fields[i + 1], fields[i]]
      }
    }
  }

  /**
   * Move selected fields to the bottom of the array
   * @param {EnhancedFieldManagerModel[]} fields - The fields array to modify
   * @param {EnhancedFieldManagerModel[]} selectedFields - The fields to move
   * @returns {void}
   */
  #moveFieldsToBottom(
    fields: EnhancedFieldManagerModel[],
    selectedFields: EnhancedFieldManagerModel[]
  ): void {
    const selectedNames = new Set(selectedFields.map((f) => f.displayFieldName))
    const selected = fields.filter((f) => selectedNames.has(f.displayFieldName))
    const unselected = fields.filter(
      (f) => !selectedNames.has(f.displayFieldName)
    )

    // Clear and rebuild array with unselected first, then selected
    fields.length = 0
    fields.push(...unselected, ...selected)
  }

  /**
   * Stable trackBy for grid rows based on field name.
   * Ensures virtualization reuses DOM nodes correctly and avoids visual glitches.
   * @param {number} _ - index (unused)
   * @param {GridItem} item - Kendo grid item wrapper
   * @returns {string} unique key for row
   */
  public trackByFieldName(_: number, item: GridItem): string {
    const data = item?.data as EnhancedFieldManagerModel
    return data?.displayFieldName
  }
}
