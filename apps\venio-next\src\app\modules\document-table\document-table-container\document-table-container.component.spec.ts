import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentTableContainerComponent } from './document-table-container.component'

import { provideMockStore } from '@ngrx/store/testing'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'
import { DocumentsFacade, SearchFacade } from '@venio/data-access/review'
import { VenioNotificationService } from '@venio/feature/notification'

describe('DocumentTableContainer', () => {
  let component: DocumentTableContainerComponent
  let fixture: ComponentFixture<DocumentTableContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        DocumentTableContainerComponent,
        StoreModule.forRoot([]),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        provideRouter([]),
        {
          provide: VenioNotificationService,
          useValue: {
            showSuccess: jest.fn(),
            showError: jest.fn(),
            showWarning: jest.fn(),
          },
        },
        {
          provide: DocumentsFacade,
          useValue: {},
        },
        {
          provide: SearchFacade,
          useValue: {},
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentTableContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
