<!-- Main container that takes full available height without causing dialog overflow -->
<div class="t-flex t-flex-col t-h-full t-w-full">
  <!-- Row 1: Section label + Case selection (fixed height, no scroll) -->
  <div
    class="t-w-full t-mb-2 t-flex t-flex-col sm:t-flex-row t-items-start sm:t-items-center t-gap-2 sm:t-gap-4 t-flex-shrink-0">
    <div class="t-text-[#263238] t-font-medium">Fields</div>
    <!-- Fields Tab Case Multi-select (mirror toolbar case dropdown UI) -->
    <div class="t-flex t-flex-col t-gap-1 t-w-full sm:t-w-80">
      <div
        class="v-infield v-static-label"
        [class.v-has-value]="selectedProjectsAsItems?.length > 0">
        <span class="v-infield-label t-text-[#00000029]" aria-hidden="true">
          <span>Select Case</span>
        </span>
        <kendo-multiselecttree
          id="fields-tab-case-select"
          #fieldsTabCaseTree
          kendoMultiSelectTreeSummaryTag
          [kendoMultiSelectTreeFlatBinding]="availableCases()"
          idField="projectId"
          parentIdField="parentId"
          textField="projectName"
          valueField="projectId"
          [valuePrimitive]="false"
          [filterable]="true"
          (filterChange)="onFieldsTabCaseFilterChange($event)"
          placeholder="Select Case"
          [popupSettings]="{ popupClass: 'custom-multiselecttree-popup' }"
          class="!t-w-full sm:!t-w-80 custom-multiselecttree"
          [value]="selectedProjectsAsItems"
          (valueChange)="onFieldsTabCasesChange($event)">
          <ng-template kendoMultiSelectTreeHeaderTemplate>
            <label
              class="t-flex t-items-center t-gap-2 t-pl-2"
              (click)="$event.stopImmediatePropagation()">
              <input
                kendoCheckBox
                type="checkbox"
                [checked]="isAllCheckedForFieldsTab()"
                [indeterminate]="isIndeterminateForFieldsTab()"
                (change)="onSelectAllChange($event)" />
              Check all
            </label>
          </ng-template>
        </kendo-multiselecttree>
      </div>
    </div>
  </div>

  <!-- Row 2: Two headings aligned in one row (fixed height, no scroll) -->
  <div
    class="t-grid t-grid-cols-1 lg:t-grid-cols-2 t-gap-4 lg:t-gap-8 t-w-full t-mb-1 t-flex-shrink-0">
    <div
      class="t-h-[30px] t-flex t-items-center t-text-[#283593] t-text-base t-font-medium">
      <div class="t-w-[44px] t-text-center t-pl-1">#</div>
      <div class="t-w-[40px] t-px-1 t-flex t-items-center">
        <input
          kendoCheckBox
          type="checkbox"
          aria-label="Select all unselected fields"
          [checked]="
            leftFields()?.total && rightFields()?.total === leftFields()?.total
          "
          [indeterminate]="
            rightFields()?.total > 0 &&
            rightFields()?.total < (leftFields()?.total || 0)
          "
          (change)="onLeftHeaderToggle($any($event.target).checked)" />
      </div>
      <div class="t-flex-1 t-pl-2">Unselected</div>
    </div>
    <div
      class="t-h-[30px] t-flex t-items-center t-text-[#283593] t-text-base t-font-medium lg:t-block">
      <span class="t-pl-6 t-ml-1"> Selected</span>
    </div>
  </div>

  <!-- Row 3: Two columns with internal scrolling - This takes remaining height -->
  <div
    class="t-grid lg:t-grid-cols-2 t-gap-4 lg:t-gap-8 t-w-full t-flex-1 t-min-h-0">
    <!-- Left: Available Fields (Unselected) with internal scroll -->
    <div class="t-w-full t-h-full t-relative t-flex t-flex-col t-min-h-0">
      <kendo-grid
        class="t-w-full t-rounded t-overflow-hidden t-flex-1 t-h-[calc(100%_-_90px)]"
        [data]="leftFields()"
        [resizable]="false"
        [sortable]="false"
        [filterable]="false"
        scrollable="virtual"
        [rowHeight]="25"
        [pageSize]="leftPageSize"
        [skip]="leftSkip"
        [hideHeader]="true"
        [trackBy]="trackByFieldName"
        kendoGridSelectBy="displayFieldName"
        [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
        [selectedKeys]="leftSelectedKeys"
        (pageChange)="onLeftPageChange($event)"
        (selectionChange)="onSelectionChangeAvailableFields($event)">
        <kendo-grid-column
          title=""
          [width]="44"
          class="!t-pl-1 !t-pr-0 !t-py-0">
          <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
            <div class="t-flex t-items-center t-justify-center">
              {{ rowIndex + 1 }}
            </div>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-checkbox-column
          [showSelectAll]="false"
          [width]="40"
          title=""
          class="!t-px-1 !t-py-0">
        </kendo-grid-checkbox-column>
        <kendo-grid-column field="displayFieldName" title="" class="!t-py-0">
          <ng-template kendoGridCellTemplate let-dataItem>
            <div class="t-text-[14px] t-text-[#37474F] t-flex t-items-center">
              {{ dataItem.displayFieldName }}
            </div>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>

    <!-- Right: Selected Fields with grid reordering -->
    <div class="t-w-full t-h-full t-flex t-flex-col t-min-h-0">
      <kendo-grid
        class="t-w-full t-min-h-[100px] t-max-h-auto v-customgrid-metafield t-flex-1"
        [data]="rightFields()"
        [resizable]="false"
        [sortable]="false"
        [filterable]="false"
        scrollable="scrollable"
        [rowHeight]="25"
        [pageSize]="rightPageSize"
        [skip]="rightSkip"
        [trackBy]="trackByFieldName"
        [hideHeader]="true"
        [rowReorderable]="true"
        (pageChange)="onRightPageChange($event)"
        (rowReorder)="onRightGridReorder($event)">
        <kendo-grid-rowreorder-column [width]="20" />
        <kendo-grid-column field="displayFieldName" title="" class="!t-py-0">
          <ng-template kendoGridCellTemplate let-dataItem>
            <div class="t-text-[14px] t-text-[#37474F] t-flex t-items-center">
              {{ dataItem.displayFieldName }}
            </div>
          </ng-template>
        </kendo-grid-column>
        <ng-template kendoGridNoRecordsTemplate>
          <div class="t-text-[#979797]">No fields selected</div>
        </ng-template>
      </kendo-grid>
    </div>
  </div>
</div>
