import { ComponentFixture, TestBed } from '@angular/core/testing'
import { BulkTagCodingAcitonDialogComponent } from './bulk-tag-coding-aciton-dialog.component'
import { EffectsModule } from '@ngrx/effects'

import { provideMockStore } from '@ngrx/store/testing'
import { CUSTOM_ELEMENTS_SCHEMA, PLATFORM_ID } from '@angular/core'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  SearchResponseModel,
  SearchResultFacade,
} from '@venio/data-access/review'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import {
  DocumentCodingState,
  DocumentTagFacade,
  TagSettings,
} from '@venio/data-access/document-utility'
import { StoreModule } from '@ngrx/store'
import { provideRouter } from '@angular/router'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import {
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { VenioNotificationService } from '@venio/feature/notification'

describe('BulkTagCodingAcitonDialogComponent', () => {
  let component: BulkTagCodingAcitonDialogComponent
  let fixture: ComponentFixture<BulkTagCodingAcitonDialogComponent>

  // Mock data for SearchResponseModel
  const mockSearchResponse: SearchResponseModel = {
    tempTables: undefined,
    error: undefined,
    searchResultIntialParameters: undefined,
  }

  // Mock data for DocumentCodingState
  const initialDocumentCodingState: DocumentCodingState = {
    documentCodingFields: [],
    fieldCodingModel: [],
    visibleCodingFields: undefined,
    updatedCodingFieldInfoIds: [],
    searchDocumentCoding: '',
    isDocumentCodeLoading: false,
    isCodingDataModified: false,
    isCodingDataValid: undefined,
    codingFailureResponse: undefined,
    selectedMultiCodingValue: undefined,
    codingActionEventType: undefined,
    bulkCodingValues: undefined,
    isBulkCodingValuesLoading: false,
    bulkCodingFailureResponse: undefined,
    isDocumentCodingUpdated: undefined,
  }

  // Mock data for searchInitialState
  const initialSearchState = {
    searchResponse: mockSearchResponse,
    breadcrumbs: null,
    mainSearchBreadcrumb: [],
    conditionalBreadcrumbs: [],
    filterBreadcrumbs: [],
    isSearchLoading: null,
    dynamicFolderSearchScope: null,
    staticFolderSearchScope: null,
  }

  // Mock data for searchResultMockData
  const mockSearchResultData = {
    searchResults: {
      ids: [1, 2, 3],
      entities: {
        1: { fileId: 1 },
        2: { fileId: 2 },
      },
      normalizedMetadata: {
        1: {
          ids: [10, 20],
          entities: {
            10: { key: 'meta1', value: 'Foo' },
            20: { key: 'meta2', value: 'Bar' },
          },
        },
        2: {
          ids: [30],
          entities: {
            30: { key: 'meta3', value: 'Baz' },
          },
        },
      },
    },
  }

  // Mock data for searchResultInitialState
  const initialSearchResultState = {
    searchResults: mockSearchResultData,
  }

  // Mock data for documentTagState
  const initialDocumentTagState = {
    projectTags: [],
    documentTags: {
      ids: [1, 2, 3],
      entities: {
        1: { id: 1, name: 'Document Tag 1' },
        2: { id: 2, name: 'Document Tag 2' },
      },
    },
    searchDocumentTags: '',
    userSelectedDocumentTag: [],
    tagsProfileCategory: [],
    projectTagSettings: {} as TagSettings,
    isDocumentTagLoading: false,
    areTagCommentsMissing: false,
    applyDocumentTagSuccessResponse: undefined,
    applyDocumentTagErrorResponse: undefined,
    tagProjectTagsErrorResponse: undefined,
    tagDocumentTagsErrorResponse: undefined,
    tagProfileCategoryErrorResponse: undefined,
    tagTagSettingErrorResponse: undefined,
  }

  // Mock data for initialState
  const initialState = {
    venioSearch: initialSearchState,
    venioDocumentTag: initialDocumentTagState,
    venioDocumentCoding: initialDocumentCodingState,
    venioSearchResult: initialSearchResultState,
    venioDocuments: {
      isBatchSelected: false,
      currentDocument: 1,
      currentDocumentName: 'mockDocument',
      currentDocumentTablePage: 0,
      selectedDocuments: [1, 2],
      unselectedDocuments: [],
      menuEventPayload: null,
      isDocumentMenuLoading: false,
      isBulkDocument: true,
    },
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        BulkTagCodingAcitonDialogComponent,
        IframeMessengerModule.forRoot({}),
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideMockStore({ initialState }),
        provideRouter([]),
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: VenioNotificationService,
          useValue: {
            showSuccess: jest.fn(),
            showError: jest.fn(),
            showWarning: jest.fn(),
          },
        },
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        DocumentsFacade,
        DocumentTagFacade,
        SearchFacade,
        FieldFacade,
        SearchResultFacade,
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(BulkTagCodingAcitonDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
