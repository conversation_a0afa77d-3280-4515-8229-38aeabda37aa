import {
  Component,
  OnInit,
  OnD<PERSON>roy,
  signal,
  inject,
  viewChild,
  input,
  output,
  HostListener,
  effect,
  runInInjectionContext,
  Injector,
  Signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ReactiveFormsModule, FormGroup, AbstractControl } from '@angular/forms'
import {
  Subject,
  takeUntil,
  debounceTime,
  distinctUntilChanged,
  filter,
  switchMap,
  startWith,
  map,
  of,
} from 'rxjs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  DropDownsModule,
  MultiSelectTreeComponent,
} from '@progress/kendo-angular-dropdowns'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LabelModule } from '@progress/kendo-angular-label'
import { chevronLeftIcon } from '@progress/kendo-svg-icons'
import {
  CompositeLayoutFacade,
  ClientModel,
  ProjectInfo,
  GroupInfo,
} from '@venio/data-access/review'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { toObservable, toSignal } from '@angular/core/rxjs-interop'

type DropdownType = 'client' | 'case' | 'userGroup'
type DropdownItem = ClientModel | ProjectInfo | GroupInfo

@Component({
  selector: 'venio-view-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonsModule,
    InputsModule,
    DropDownsModule,
    IconsModule,
    LabelModule,
  ],
  templateUrl: './view-toolbar.component.html',
  styleUrl: './view-toolbar.component.scss',
})
export class ViewToolbarComponent implements OnInit, OnDestroy {
  private readonly layoutFacade = inject(CompositeLayoutFacade)

  private readonly injector = inject(Injector)

  private readonly toDestroy$ = new Subject<void>()

  public formGroup = input.required<FormGroup>()

  public backClicked = output<void>()

  /** Controls visibility of the Back button (defaults to false). */
  public showBackButton = input(false)

  public readonly icons = { chevronLeftIcon }

  public multiSelectClientTreeRef = viewChild<MultiSelectTreeComponent>(
    'multiSelectClientTree'
  )

  public multiSelectCaseTreeRef = viewChild<MultiSelectTreeComponent>(
    'multiSelectCaseTree'
  )

  public multiSelectGroupTreeRef = viewChild<MultiSelectTreeComponent>(
    'multiSelectGroupTree'
  )

  /** A signal that is `true` if the component is in "edit" mode (viewId > 0). */
  public isLocked: Signal<boolean> = toSignal(
    toObservable(this.formGroup).pipe(
      filter((form): form is FormGroup => !!form),
      switchMap((form) => form.valueChanges.pipe(startWith(form.value))),
      map((value) => (value?.viewId ?? 0) > 0),
      distinctUntilChanged()
    ),
    { initialValue: false }
  )

  /** A signal that is `true` if the "ME" (isPrivate) toggle is active. */
  public isPrivate: Signal<boolean> = toSignal(
    toObservable(this.formGroup).pipe(
      filter((form): form is FormGroup => !!form),
      switchMap((form) => {
        const ctrl = form.get('isPrivate')
        return ctrl ? ctrl.valueChanges.pipe(startWith(ctrl.value)) : of(false)
      }),
      map((value) => !!value),
      distinctUntilChanged()
    ),
    { initialValue: false }
  )

  public clients = signal<ClientModel[]>([])

  public projects = signal<ProjectInfo[]>([])

  public groups = signal<GroupInfo[]>([])

  public isClientsLoading = signal(false)

  public isCasesLoading = signal(false)

  public isGroupsLoading = signal(false)

  private fetchCaseAction = new Subject<number[]>()

  private fetchUserGroupAction = new Subject<number[]>()

  private clientFilterTerm = ''

  private caseFilterTerm = ''

  private userGroupFilterTerm = ''

  /**
   * Kicks off the component's initialization sequence by fetching initial
   * data and setting up all reactive subscriptions and effects.
   * @returns {void}
   */
  public ngOnInit(): void {
    this.fetchClients()
    this.setupReactiveBehaviors()
  }

  /**
   * Completes the `toDestroy$` subject to clean up all active RxJS subscriptions,
   * preventing memory leaks when the component is destroyed.
   * @returns {void}
   */
  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Emits the `backClicked` event when the user clicks the back button.
   * @returns {void}
   */
  public onBackClick(): void {
    this.backClicked.emit()
  }

  /**
   * Ensures only one Kendo dropdown is open at a time by closing all others.
   * This is a UI/UX enhancement to prevent a cluttered interface.
   * @param {MultiSelectTreeComponent} openedTree - The tree component that was just opened.
   * @returns {void}
   */
  public onDropdownOpened(openedTree: MultiSelectTreeComponent): void {
    this.getAllTrees().forEach((tree) => {
      if (tree !== openedTree && tree.isOpen) {
        tree.toggle(false)
      }
    })
  }

  /**
   * Updates the internal state for a dropdown's filter term. This state is later
   * used by `getFilteredItems` to perform client-side filtering.
   * @param {DropdownType} type - The identifier for the dropdown being filtered.
   * @param {string} term - The filter text entered by the user.
   * @returns {void}
   */
  public onFilterChange(type: DropdownType, term: string): void {
    switch (type) {
      case 'client':
        this.clientFilterTerm = term
        break
      case 'case':
        this.caseFilterTerm = term
        break
      case 'userGroup':
        this.userGroupFilterTerm = term
        break
    }
  }

  /**
   * Toggles the selection of all visible items in a dropdown. Its logic is
   * dependent on the current filter term for that dropdown. It updates both the
   * Angular form control and the Kendo component's value for UI consistency.
   * @param {DropdownType} type - The dropdown being toggled.
   * @param {MultiSelectTreeComponent} tree - The Kendo component instance.
   * @param {Event} event - The checkbox click event.
   * @returns {void}
   */
  public toggleCheckAll(
    type: DropdownType,
    tree: MultiSelectTreeComponent,
    event: Event
  ): void {
    const target = event.target as HTMLInputElement
    const control = this.getControlByType(type)
    if (!control) return

    const itemsToSet = target.checked ? this.getFilteredItems(type) : []
    control.setValue(itemsToSet)
    tree.value = itemsToSet
  }

  /**
   * Determines if all filtered items in a dropdown are currently selected.
   * Used to set the state of the "Select All" checkbox.
   * @param {DropdownType} type - The dropdown to check.
   * @returns {boolean} `true` if the selection count matches the filtered item count.
   */
  public isAllSelected(type: DropdownType): boolean {
    const selected = this.getControlByType(type)?.value
    const filtered = this.getFilteredItems(type)
    return (
      Array.isArray(selected) &&
      selected.length > 0 &&
      selected.length === filtered.length
    )
  }

  /**
   * Determines if all filtered items in a dropdown are checked.
   * This method is preserved for API compatibility with the original codebase.
   * @param {DropdownType} type - The dropdown to check.
   * @returns {boolean} `true` if the selection count matches the filtered item count.
   */
  public isAllChecked(type: DropdownType): boolean {
    const selected = this.getControlByType(type)?.value
    const filtered = this.getFilteredItems(type)
    return (
      Array.isArray(selected) &&
      selected.length > 0 &&
      selected.length === filtered.length
    )
  }

  /**
   * Determines if a dropdown's selection is partial (not empty, but not all items selected).
   * Used to set the "indeterminate" state of the "Select All" checkbox.
   * @param {DropdownType} type - The dropdown to check.
   * @returns {boolean} `true` if the selection is partial.
   */
  public isIndeterminate(type: DropdownType): boolean {
    const selected = this.getControlByType(type)?.value
    const filtered = this.getFilteredItems(type)
    return (
      Array.isArray(selected) &&
      selected.length > 0 &&
      selected.length < filtered.length
    )
  }

  /**
   * Closes all dropdowns when a click occurs anywhere in the document outside
   * of the dropdowns themselves or their popups.
   * @param {MouseEvent} event - The `document:click` event.
   * @returns {void}
   */
  @HostListener('document:click', ['$event'])
  public handleDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement
    const isInsideAnyPopup =
      target?.closest?.('.custom-multiselecttree-popup') !== null
    const isInsideAnyWrapper = this.getAllTrees().some((tree) =>
      tree?.wrapper?.nativeElement?.contains(target)
    )
    if (isInsideAnyPopup || isInsideAnyWrapper) return
    this.getAllTrees().forEach((tree) => tree.toggle(false))
  }

  /**
   * Orchestrates the initialization of all reactive logic for the component.
   * @private
   * @returns {void}
   */
  private setupReactiveBehaviors(): void {
    this.setupClientChangeHandler()
    this.setupCaseChangeHandler()
    this.setupCaseFetching()
    this.setupUserGroupFetching()
    this.setupPrivateModeDataResetHandler()
    this.setupControlStateEffect()
  }

  /**
   * This `effect` is the declarative core of the component's UI state logic.
   * It automatically synchronizes the enabled/disabled state of form controls
   * whenever its dependent signals (`isLocked`, `isPrivate`) change. It delegates
   * the specific logic to helper methods to maintain clarity.
   * @private
   * @returns {void}
   */
  private setupControlStateEffect(): void {
    runInInjectionContext(this.injector, () => {
      effect(() => {
        const isLocked = this.isLocked()
        const isPrivate = this.isPrivate()

        this.formGroup()
          .get('isPrivate')
          ?.[isLocked ? 'disable' : 'enable']({ emitEvent: false })

        if (isLocked) {
          this.applyEditModeControlState(isPrivate)
        } else {
          this.applyCreateModeControlState(isPrivate)
        }
      })
    })
  }

  /**
   * Applies the specific enable/disable logic for controls when in Edit Mode.
   * If "ME" is selected, all dropdowns are disabled; otherwise, they are enabled.
   * @private
   * @param {boolean} isPrivate - The current state of the "ME" toggle.
   * @returns {void}
   */
  private applyEditModeControlState(isPrivate: boolean): void {
    this.setDropdownsEnabledState(!isPrivate)
  }

  /**
   * Applies the specific enable/disable logic for controls when in Create Mode.
   * This follows the cascading logic: Client is enabled (unless "ME" is on),
   * Case is enabled if projects are loaded, and Group is enabled if groups are loaded.
   * @private
   * @param {boolean} isPrivate - The current state of the "ME" toggle.
   * @returns {void}
   */
  private applyCreateModeControlState(isPrivate: boolean): void {
    if (isPrivate) {
      this.setDropdownsEnabledState(false)
    } else {
      const hasProjects = this.projects().length > 0
      const hasGroups = this.groups().length > 0
      this.formGroup().get('client')?.enable({ emitEvent: false })
      this.formGroup()
        .get('case')
        ?.[hasProjects ? 'enable' : 'disable']({ emitEvent: false })
      this.formGroup()
        .get('group')
        ?.[hasGroups ? 'enable' : 'disable']({ emitEvent: false })
    }
  }

  /**
   * A utility to enable or disable all three main dropdown controls simultaneously.
   * @private
   * @param {boolean} isEnabled - Whether to enable or disable the controls.
   * @returns {void}
   */
  private setDropdownsEnabledState(isEnabled: boolean): void {
    const action = isEnabled ? 'enable' : 'disable'
    this.formGroup().get('client')?.[action]({ emitEvent: false })
    this.formGroup().get('case')?.[action]({ emitEvent: false })
    this.formGroup().get('group')?.[action]({ emitEvent: false })
  }

  /**
   * Subscribes to user-driven changes of the 'isPrivate' control. When the user
   * toggles "ME" to true, this performs a one-time reset of dependent data and UI.
   * This is an event-driven subscription, not a continuous signal effect.
   * @private
   * @returns {void}
   */
  private setupPrivateModeDataResetHandler(): void {
    this.formGroup()
      .get('isPrivate')
      ?.valueChanges.pipe(
        filter((isPrivate): isPrivate is boolean => isPrivate === true),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.formGroup().patchValue(
          { client: [], case: [], group: [] },
          { emitEvent: false }
        )
        this.projects.set([])
        this.groups.set([])
        this.getAllTrees().forEach((tree) => tree.toggle(false))
      })
  }

  /**
   * Fetches the initial list of clients from the data facade and updates the `clients` signal.
   * @private
   * @returns {void}
   */
  private fetchClients(): void {
    this.isClientsLoading.set(true)
    this.layoutFacade
      .fetchClients$<ResponseModel>()
      .pipe(takeUntil(this.toDestroy$))
      .subscribe({
        next: (response: ResponseModel) => {
          const data = (
            Array.isArray(response?.data) ? response.data : []
          ) as ClientModel[]
          this.clients.set(data.map((c) => ({ ...c, parentId: null })))
          this.isClientsLoading.set(false)
        },
        error: () => this.isClientsLoading.set(false),
      })
  }

  /**
   * Subscribes to changes in the client dropdown. When a selection is made, it
   * resets the dependent case and group data/controls and triggers a fetch for new cases.
   * @private
   * @returns {void}
   */
  private setupClientChangeHandler(): void {
    this.formGroup()
      .get('client')
      ?.valueChanges.pipe(
        filter((clients): clients is ClientModel[] => Array.isArray(clients)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((clients) => {
        this.projects.set([])
        this.groups.set([])
        this.formGroup().patchValue(
          { case: [], group: [] },
          { emitEvent: false }
        )

        if (clients.length > 0) {
          this.fetchCaseAction.next(clients.map((c) => c.clientId))
        }
      })
  }

  /**
   * Subscribes to changes in the case dropdown. When a selection is made, it
   * resets the dependent group data/control and triggers a fetch for new groups.
   * @private
   * @returns {void}
   */
  private setupCaseChangeHandler(): void {
    this.formGroup()
      .get('case')
      ?.valueChanges.pipe(
        filter((projects): projects is ProjectInfo[] =>
          Array.isArray(projects)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((projects) => {
        this.groups.set([])
        this.formGroup().patchValue({ group: [] }, { emitEvent: false })

        if (projects.length > 0) {
          this.fetchUserGroupAction.next(projects.map((p) => p.projectId))
        }
      })
  }

  /**
   * Sets up an RxJS pipeline that listens for `fetchCaseAction` events.
   * It uses `debounceTime` to prevent excessive API calls and `switchMap` to
   * cancel previous, outdated requests, ensuring only the result for the latest
   * selection is processed.
   * @private
   * @returns {void}
   */
  private setupCaseFetching(): void {
    this.fetchCaseAction
      .pipe(
        filter((clientIds) => clientIds.length > 0),
        debounceTime(200),
        distinctUntilChanged(),
        switchMap((clientIds) => {
          this.isCasesLoading.set(true)
          return this.layoutFacade.fetchCasesByClientIds<ProjectInfo[]>({
            clientIds,
          })
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (projects) => {
          this.projects.set(projects.map((p) => ({ ...p, parentId: null })))
          this.isCasesLoading.set(false)
        },
        error: () => this.isCasesLoading.set(false),
      })
  }

  /**
   * Sets up an RxJS pipeline that listens for `fetchUserGroupAction` events.
   * It uses `debounceTime` and `switchMap` for performance and to prevent race
   * conditions, similar to the case fetching pipeline.
   * @private
   * @returns {void}
   */
  private setupUserGroupFetching(): void {
    this.fetchUserGroupAction
      .pipe(
        debounceTime(200),
        distinctUntilChanged(),
        switchMap((caseIds) => {
          this.isGroupsLoading.set(true)
          return this.layoutFacade.fetchProjectUserGroups$<ResponseModel>(
            caseIds
          )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (response) => {
          const userGroups = (
            Array.isArray(response?.data) ? response.data : []
          ) as GroupInfo[]
          this.groups.set(userGroups.map((ug) => ({ ...ug, parentId: null })))
          this.isGroupsLoading.set(false)
        },
        error: () => this.isGroupsLoading.set(false),
      })
  }

  /**
   * A utility to retrieve the form control instance for a given dropdown type.
   * @private
   * @param {DropdownType} type - The identifier for the dropdown.
   * @returns {AbstractControl | null} The corresponding form control.
   */
  private getControlByType(type: DropdownType): AbstractControl | null {
    const controlName = type === 'userGroup' ? 'group' : type
    return this.formGroup().get(controlName)
  }

  /**
   * A utility to get a list of all active Kendo tree component instances.
   * @private
   * @returns {MultiSelectTreeComponent[]} An array of the component instances.
   */
  private getAllTrees(): MultiSelectTreeComponent[] {
    return [
      this.multiSelectClientTreeRef?.(),
      this.multiSelectCaseTreeRef?.(),
      this.multiSelectGroupTreeRef?.(),
    ].filter((t): t is MultiSelectTreeComponent => Boolean(t))
  }

  /**
   * Returns a filtered list of items for a specific dropdown based on the user-entered
   * filter term. The filtering is case-insensitive.
   * @private
   * @param {DropdownType} type - The dropdown for which to get filtered items.
   * @returns {DropdownItem[]} The filtered list of items.
   */
  private getFilteredItems(type: DropdownType): DropdownItem[] {
    switch (type) {
      case 'client': {
        const clientTerm = this.clientFilterTerm.trim().toLowerCase()
        const clients = this.clients()
        return clientTerm
          ? clients.filter((c) =>
              (c.clientName || '').toLowerCase().includes(clientTerm)
            )
          : clients
      }
      case 'case': {
        const caseTerm = this.caseFilterTerm.trim().toLowerCase()
        const projects = this.projects()
        return caseTerm
          ? projects.filter((p) =>
              (p.projectName || '').toLowerCase().includes(caseTerm)
            )
          : projects
      }
      case 'userGroup': {
        const groupTerm = this.userGroupFilterTerm.trim().toLowerCase()
        const groups = this.groups()
        return groupTerm
          ? groups.filter((g) =>
              (g.groupName || '').toLowerCase().includes(groupTerm)
            )
          : groups
      }
    }
  }
}
