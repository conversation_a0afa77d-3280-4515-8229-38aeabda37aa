import {
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  computed,
  inject,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { ActivatedRoute } from '@angular/router'
import {
  Observable,
  Subject,
  combineLatest,
  forkJoin,
  of,
  startWith,
  merge,
} from 'rxjs'
import { filter, map, switchMap, take, takeUntil } from 'rxjs/operators'

import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { TabStripModule } from '@progress/kendo-angular-layout'
import { IconsModule } from '@progress/kendo-angular-icons'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { DialogsModule, WindowRef } from '@progress/kendo-angular-dialog'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { chevronLeftIcon } from '@progress/kendo-svg-icons'

import {
  Accessibility,
  Field,
  FieldFacade,
  ProjectInfo,
  ViewField,
  ViewModel,
  ViewSortSetting,
  ViewType,
  ViewFacade,
  SearchFacade,
  FieldService,
  ViewService,
  CompositeLayoutFacade,
} from '@venio/data-access/review'
import { DocumentViewFacade, UserFacade } from '@venio/data-access/common'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { toSignal, toObservable } from '@angular/core/rxjs-interop'

import { ViewToolbarComponent } from '../view-toolbar/view-toolbar.component'
import { ViewFieldSelectorComponent } from '../view-field-selector/view-field-selector.component'
import { ViewConditionsBuilderComponent } from '../view-conditions-builder/view-conditions-builder.component'
import { ViewSortBuilderComponent } from '../view-sort-builder/view-sort-builder.component'
import { isEqual } from 'lodash'

/** Narrowed models from backend responses used locally. */
type ClientRow = { clientId: number }
type ProjectGroupRow = { projectId: number; groupId: number }
type CustomFieldRow = {
  customFieldId: number
  displayName: string
  fieldName: string
  uiInputType: string
  fieldGroup?: string
  allowNullSearch?: boolean
  allowCoding?: boolean
  isSearchable?: boolean
}

/** Field with project provenance for dedupe/merge. */
type FieldWithProject = Field & {
  projectId?: number
  sourceProjects?: number[]
}

/** Mapping for project-to-user-group associations used during save. */
interface ViewProjectOrUserGroup {
  projectId: number
  userGroupIds: number[]
}

@Component({
  selector: 'venio-view-management-container',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonsModule,
    InputsModule,
    DropDownsModule,
    TabStripModule,
    IconsModule,
    IndicatorsModule,
    DialogsModule,
    SvgLoaderDirective,
    ViewToolbarComponent,
    ViewFieldSelectorComponent,
    ViewConditionsBuilderComponent,
    ViewSortBuilderComponent,
  ],
  templateUrl: './view-management-container.component.html',
  styleUrl: './view-management-container.component.scss',
})
export class ViewManagementContainerComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>()

  private readonly fb = inject(FormBuilder)

  private readonly fieldFacade = inject(FieldFacade)

  private readonly userFacade = inject(UserFacade)

  private readonly fieldService = inject(FieldService)

  private readonly layoutFacade = inject(CompositeLayoutFacade)

  public readonly windowRef = inject(WindowRef, { optional: true })

  private readonly documentViewFacade = inject(DocumentViewFacade)

  private readonly activatedRoute = inject(ActivatedRoute)

  private readonly notificationService = inject(NotificationService)

  private readonly viewFacade = inject(ViewFacade)

  private readonly searchFacade = inject(SearchFacade)

  private readonly viewService = inject(ViewService)

  public readonly icons = { chevronLeftIcon }

  /** True if this form was launched from listing window; controls Back visibility and navigation context. */
  public fromListing = false

  public viewFormGroup: FormGroup

  /** Signal to trigger form value changes */
  private readonly formInitialized = signal(false)

  public readonly isSaving = signal(false)

  /** Selected fields (Fields tab). */
  public readonly selectedFields = signal<ViewField[]>([])

  /** System (Venio) fields (loaded once). */
  public readonly allSystemFields = signal<Field[]>([])

  /** Fields available to the Fields tab (system + custom per selected projects). */
  public readonly fieldsForFieldsTab = signal<Field[]>([])

  /** Fields available to the Sort tab (all system fields). */
  public readonly fieldsForSortTab = signal<Field[]>([])

  /** Fields available to the Conditions tab (all system fields). */
  public readonly fieldsForConditionTab = signal<Field[]>([])

  /** Cases available to pick in the Fields tab (varies with ME toggle and toolbar case selection). */
  public readonly fieldsTabAvailableCases = signal<ProjectInfo[]>([])

  /** Currently selected project IDs within the Fields tab’s internal multi-select. */
  public readonly fieldsTabSelectedProjectIds = signal<number[]>([])

  /** Snapshot of cross-tab form data stored in facade (conditions, sorts, etc.). */
  public readonly currentView = signal<ViewModel | null>(null)

  /** TabStrip active index. */
  public readonly activeTab = signal(0)

  /**
   * Validity gate for enabling TABS navigation and SAVE button.
   * @returns {boolean} - True if the form can be saved, false otherwise.
   */
  private get routeProjectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId'] || 0
  }

  /**
   * Validity gate for enabling TABS navigation and SAVE button.
   * @returns {boolean} - True if the form can be saved, false otherwise.
   */
  public canConfigureTabs(): boolean {
    // Tab enablement should ONLY depend on toolbar criteria, NOT field selection
    const form = this.formValue()

    const hasName = !!String(form?.viewName || '').trim()
    const meOn = form?.isPrivate === true
    const hasAllDropdowns =
      Array.isArray(form?.client) &&
      form.client.length > 0 &&
      Array.isArray(form?.case) &&
      form.case.length > 0 &&
      Array.isArray(form?.group) &&
      form.group.length > 0

    // Tabs should enable when toolbar criteria are met, regardless of field selection
    return hasName && (meOn || hasAllDropdowns)
  }

  /** Form value for reactive validation - includes disabled controls */
  private readonly formValue = toSignal(
    toObservable(this.formInitialized).pipe(
      filter(Boolean),
      switchMap(() =>
        merge(
          this.viewFormGroup.valueChanges,
          this.viewFormGroup.statusChanges
        ).pipe(
          startWith(this.viewFormGroup.getRawValue()),
          map(() => this.viewFormGroup.getRawValue())
        )
      )
    ),
    {
      initialValue: {
        viewName: '',
        isPrivate: false,
        client: [],
        case: [],
        group: [],
      },
      equal: isEqual,
    }
  )

  /** Unified validity gate for SAVE button - replaces both canConfigureTabs and formValidation */
  private readonly formValidation = computed<boolean>(() => {
    // Get form data using getRawValue to include disabled controls (important for edit mode)
    const form = this.formValue()

    // Basic validation: must have view name
    const hasName = !!String(form?.viewName || '').trim()
    if (!hasName) return false

    // ME (isPrivate) mode validation
    const meOn = form?.isPrivate === true

    // Public mode validation: requires all 3 dropdown controls to have values
    const hasAllDropdowns =
      Array.isArray(form?.client) &&
      form.client.length > 0 &&
      Array.isArray(form?.case) &&
      form.case.length > 0 &&
      Array.isArray(form?.group) &&
      form.group.length > 0

    // Field selection validation: Check both selectedFields signal and store data
    const selectedFieldsCount = this.selectedFields().length
    const storeViewFields = this.currentView()?.viewFields || []
    const hasAtLeastOneField =
      selectedFieldsCount > 0 || storeViewFields.length > 0

    // Final validation logic per requirements:
    // 1. Must have view name AND at least one field selected
    // 2. If ME is true, dropdown validation is bypassed
    // 3. If ME is false, all 3 dropdowns must have values
    return hasName && hasAtLeastOneField && (meOn || hasAllDropdowns)
  })

  public get isFormValid(): boolean {
    return this.formValidation()
  }

  constructor() {
    this.viewFormGroup = this.buildForm()
    this.formInitialized.set(true)
  }

  public ngOnInit(): void {
    this.initConditionalValidators()
    this.bridgeTopFormToStore()
    this.hydrateCurrentViewSnapshot()
    this.initSystemFields()
    this.initClientWatcher()
    this.initCaseWatcher()
    this.initMeWatcher()
  }

  public ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }

  /**
   * Handles Kendo TabStrip selection.
   * @param {number} index - Selected tab index.
   * @returns {void}
   */
  public onTabSelect(index: number): void {
    this.activeTab.set(index)
  }

  /** Exposes controls to the template. */
  public get formControls(): {
    [key: string]: AbstractControl<unknown, unknown>
  } {
    return this.viewFormGroup.controls
  }

  public cancel(): void {
    this.windowRef?.close()
  }

  public goBack(): void {
    this.windowRef?.close()
  }

  /**
   * Save handler: validate → build payload → persist → monitor responses.
   * @returns {void}
   */
  public save(): void {
    if (!this.isFormValid) {
      this.markAllTouched(this.viewFormGroup)
      return
    }
    this.isSaving.set(true)
    const payload = this.buildViewPayload()
    this.persistView(payload)
  }

  /**
   * Fields Tab project selection changed (child output).
   * @param {number[]} projectIds - Selected project IDs from the Fields tab.
   * @returns {void}
   */
  public onFieldsTabCaseSelectionChange(projectIds: number[]): void {
    this.fieldsTabSelectedProjectIds.set(projectIds || [])
    this.loadAndMergeCustomFields(projectIds || [])
  }

  /**
   * Builds the root form group.
   * @returns {FormGroup} - The constructed FormGroup.
   */
  private buildForm(): FormGroup {
    return this.fb.group({
      viewId: 0,
      viewName: ['', [Validators.required, Validators.minLength(1)]],
      client: [[], Validators.required],
      case: [[], Validators.required],
      group: [[], Validators.required],
      isPrivate: [false],
    })
  }

  /**
   * Applies conditional validators to client/case/group when ME toggles.
   * @returns {void}
   */
  private initConditionalValidators(): void {
    const clientCtrl = this.viewFormGroup.get('client')
    const caseCtrl = this.viewFormGroup.get('case')
    const groupCtrl = this.viewFormGroup.get('group')

    this.viewFormGroup
      .get('isPrivate')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((isPrivate: boolean) => {
        if (isPrivate) {
          clientCtrl?.clearValidators()
          caseCtrl?.clearValidators()
          groupCtrl?.clearValidators()
        } else {
          clientCtrl?.setValidators([Validators.required])
          caseCtrl?.setValidators([Validators.required])
          groupCtrl?.setValidators([Validators.required])
        }
        // Enable events to ensure signal reactivity after validation changes
        clientCtrl?.updateValueAndValidity({ emitEvent: true })
        caseCtrl?.updateValueAndValidity({ emitEvent: true })
        groupCtrl?.updateValueAndValidity({ emitEvent: true })
      })
  }

  /**
   * Bridges top-bar controls into the shared store for cross-tab state.
   * @returns {void}
   */
  private bridgeTopFormToStore(): void {
    this.viewFormGroup.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((form) => {
        const isPrivate = form?.isPrivate === true
        const accessibility = isPrivate
          ? Accessibility.Private
          : Accessibility.Public
        const viewUserIds = isPrivate ? [] : [-1]
        this.documentViewFacade.storeCurrentFormData({
          viewName: form?.viewName || '',
          viewType: ViewType.Document,
          isDefault: false,
          accessibility,
          viewUserIds,
        })
      })
  }

  /**
   * Hydrates local signals with the latest cross-tab snapshot
   * and wires selected view (edit path).
   * @returns {void}
   */
  private hydrateCurrentViewSnapshot(): void {
    this.documentViewFacade.selectCurrentFormData$
      .pipe(takeUntil(this.destroy$))
      .subscribe((form) => this.currentView.set(form ?? null))

    this.documentViewFacade.selectSelectedDocumentView$
      .pipe(filter(Boolean), takeUntil(this.destroy$))
      .subscribe((view) => this.applySelectedView(view))
  }

  /**
   * Applies selected view to form/store and pre-fills toolbar selectors.
   * @param {ViewModel} view - Selected view model from store.
   * @returns {void}
   */
  private applySelectedView(view: ViewModel): void {
    const isPrivate = view?.accessibility === Accessibility.Private
    this.viewFormGroup.patchValue(
      {
        viewName: view?.viewName || '',
        isPrivate,
        viewId: view?.viewId || 0,
      },
      { emitEvent: true }
    )

    this.documentViewFacade.storeCurrentFormData({
      viewId: view?.viewId || 0,
      createdBy: view?.createdBy,
      viewName: view?.viewName,
      viewType: view?.viewType,
      isDefault: !!view?.isDefault,
      accessibility: view?.accessibility,
      viewUserIds: Array.isArray(view?.viewUserIds) ? view.viewUserIds : [],
      viewFields: Array.isArray(view?.viewFields) ? view.viewFields : [],
      viewExpression: view?.viewExpression || 'INTERNAL_FILE_ID>0',
      viewExpressionJson: view?.viewExpressionJson || '[]',
      viewSortSettings: Array.isArray(view?.viewSortSettings)
        ? view.viewSortSettings
        : [],
      viewProjectIds: Array.isArray(view?.viewProjectIds)
        ? view.viewProjectIds
        : [],
    })

    const viewId = Number(view?.viewId || 0)
    if (viewId > 0) this.prefillToolbarSelectionsFromView(viewId)
  }

  /**
   * Clears case list when clients change (ensures consistency).
   * @returns {void}
   */
  private initClientWatcher(): void {
    this.viewFormGroup
      .get('client')
      ?.valueChanges.pipe(
        filter((clients) => Array.isArray(clients)),
        takeUntil(this.destroy$)
      )
      .subscribe(() => this.fieldsTabAvailableCases.set([]))
  }

  /**
   * Handles Case selection changes from the toolbar:
   * updates fields tab, fetches custom fields, and mirrors to store.
   * @returns {void}
   */
  private initCaseWatcher(): void {
    this.viewFormGroup
      .get('case')
      ?.valueChanges.pipe(
        filter((projects) => Array.isArray(projects)),
        takeUntil(this.destroy$)
      )
      .subscribe((projects) =>
        this.onToolbarCaseChange(projects as ProjectInfo[])
      )
  }

  /**
   * Handles ME toggle changes to compute available cases scope.
   * @returns {void}
   */
  private initMeWatcher(): void {
    this.viewFormGroup
      .get('isPrivate')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((isPrivate: boolean) => this.onMeToggle(isPrivate))
  }

  /**
   * Reacts to Case selection change.
   * @param {ProjectInfo[]} projects - Selected projects from the toolbar.
   * @returns {void}
   */
  private onToolbarCaseChange(projects: ProjectInfo[]): void {
    const projectIds = (projects ?? []).map((p) => p.projectId)
    this.fieldsTabSelectedProjectIds.set(projectIds)
    this.loadAndMergeCustomFields(projectIds)

    this.documentViewFacade.storeCurrentFormData({ viewProjectIds: projectIds })

    const isPrivate = this.viewFormGroup.get('isPrivate')?.value === true
    if (!isPrivate) {
      const normalized = (projects || []).map((p) => ({
        ...p,
        parentId: null as number | null,
      }))
      this.fieldsTabAvailableCases.set(normalized)
    }
  }

  /**
   * Reacts to ME toggle. If true, expands cases to all; else mirrors current toolbar Case.
   * @param {boolean} isPrivate - Current ME toggle state.
   * @returns {void}
   */
  private onMeToggle(isPrivate: boolean): void {
    if (!isPrivate) {
      const projects = this.viewFormGroup.get('case')?.value || []
      const normalized = (Array.isArray(projects) ? projects : []).map(
        (p: ProjectInfo) => ({ ...p, parentId: null as number | null })
      )
      this.fieldsTabAvailableCases.set(normalized)
      return
    }

    this.layoutFacade
      .fetchClients$<ResponseModel>()
      .pipe(
        take(1),
        map((resp) => {
          const clients = Array.isArray(resp?.data)
            ? (resp.data as ClientRow[])
            : []
          return clients.map((c) => c.clientId).filter(Boolean)
        }),
        switchMap((clientIds) => {
          if (!clientIds.length) {
            this.fieldsTabAvailableCases.set([])
            return of<ProjectInfo[]>([])
          }
          return this.layoutFacade
            .fetchCasesByClientIds<ProjectInfo[]>({ clientIds })
            .pipe(take(1))
        }),
        takeUntil(this.destroy$)
      )
      .subscribe((projects) => {
        const normalized = (projects || []).map((p) => ({
          ...p,
          parentId: null as number | null,
        }))
        this.fieldsTabAvailableCases.set(normalized)
      })
  }

  /**
   * Loads system fields once and prepares per-tab lists.
   * @returns {void}
   */
  private initSystemFields(): void {
    this.fieldFacade.fetchAllVenioFields()
    this.fieldFacade.selectAllVenioFields$
      .pipe(takeUntil(this.destroy$))
      .subscribe((fields) => {
        const list = fields || []
        this.allSystemFields.set(list)
        this.fieldsForSortTab.set(list)
        this.fieldsForConditionTab.set(list)
        this.rebuildFieldsTabFields()
      })
  }

  /**
   * Builds the field list for the Fields tab from current system & known custom sets.
   * @returns {void}
   */
  private rebuildFieldsTabFields(): void {
    const merged = this.dedupeFieldsWithProjectTracking([
      ...(this.allSystemFields() || []),
    ])
    this.fieldsForFieldsTab.set(merged)
  }

  /**
   * Fetches custom fields for given projects and merges them with system fields.
   * @param {number[]} projectIds - Project IDs to fetch custom fields for.
   * @returns {void}
   */
  private loadAndMergeCustomFields(projectIds: number[]): void {
    if (!projectIds?.length) {
      this.fieldsForFieldsTab.set(this.allSystemFields() || [])
      return
    }

    const requests = projectIds.map((pid) =>
      this.fieldService
        .fetchAllCustomFields$<ResponseModel>(pid, true)
        .pipe(map((res) => ({ pid, res })))
    )

    forkJoin(requests)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (results) => {
          const system = this.allSystemFields() || []
          const allCustom: Field[] = results.flatMap(({ pid, res }) => {
            const data = Array.isArray(res?.data)
              ? (res.data as CustomFieldRow[])
              : []
            return data.map((f) => ({
              venioFieldId: f.customFieldId,
              displayFieldName: f.displayName,
              internalFieldName: f.fieldName,
              fieldDataType: f.uiInputType,
              fieldGroup: f.fieldGroup,
              allowNullSearch: f.allowNullSearch,
              isCustomField: true,
              allowCoding: !!f.allowCoding,
              isSearchableField: !!f.isSearchable,
              fieldGroupId: 4,
              displayOrder: 0,
              searchDataType: String(f.uiInputType || ''),
              id: Number(`${pid}${f.customFieldId}`),
              projectId: pid,
            })) as Field[]
          })

          const merged = this.dedupeFieldsWithProjectTracking([
            ...system,
            ...allCustom,
          ])
          this.fieldsForFieldsTab.set(merged)
        },
        error: () => this.fieldsForFieldsTab.set(this.allSystemFields() || []),
      })
  }

  /**
   * Deduplicates fields by logical key; custom wins; tracks project provenance.
   * @param {Field[]} fields - List of fields to deduplicate.
   * @returns {Field[]} - Deduplicated list of fields.
   */
  private dedupeFieldsWithProjectTracking(fields: Field[]): Field[] {
    const fieldMap = new Map<string, FieldWithProject>()
    for (const field of fields) {
      const key = (
        field.internalFieldName ||
        field.displayFieldName ||
        ''
      ).toLowerCase()
      if (!key) continue

      const existing = fieldMap.get(key)

      if (!existing) {
        fieldMap.set(key, field as FieldWithProject)
        continue
      }

      const curr = field as FieldWithProject
      if (curr.isCustomField && existing.isCustomField) {
        const a = existing.projectId
        const b = curr.projectId
        if (a && b && a !== b) {
          fieldMap.set(key, {
            ...existing,
            sourceProjects: Array.from(
              new Set([...(existing.sourceProjects || [a]), b])
            ),
          })
        }
      } else if (curr.isCustomField && !existing.isCustomField) {
        fieldMap.set(key, curr)
      }
    }
    return Array.from(fieldMap.values())
  }

  /**
   * Prefills toolbar selectors for a given viewId by loading the (client→projects→groups) mapping.
   * @param {number} viewId - The view ID being edited.
   * @returns {void}
   */
  private prefillToolbarSelectionsFromView(viewId: number): void {
    this.fetchViewClientProjectGroups(viewId)
      .pipe(
        switchMap((mapping) =>
          this.fetchAndPatchClientsForMapping(mapping).pipe(
            map(
              ({
                mapping,
                clientIds,
                selectedProjectIds,
                groupIdsByProject,
              }) => ({
                mapping,
                clientIds,
                selectedProjectIds,
                groupIdsByProject,
              })
            )
          )
        ),
        switchMap(({ clientIds, selectedProjectIds, groupIdsByProject }) =>
          this.fetchAndPatchProjectsForMapping(
            clientIds,
            selectedProjectIds
          ).pipe(map(() => ({ selectedProjectIds, groupIdsByProject })))
        ),
        switchMap(({ selectedProjectIds, groupIdsByProject }) =>
          this.fetchAndPatchGroupsForMapping(
            selectedProjectIds,
            groupIdsByProject
          )
        ),
        take(1),
        takeUntil(this.destroy$)
      )
      .subscribe(({ groupsResp, groupIdsByProject }) => {
        const groups = Array.isArray(groupsResp?.data)
          ? (groupsResp.data as ProjectGroupRow[])
          : []
        const selectedGroups = groups.filter((g) => {
          const ids = groupIdsByProject.get(g.projectId) || []
          return ids.includes(g.groupId)
        })
        const normalized = selectedGroups.map((g) => ({
          ...g,
          parentId: null as number | null,
        }))
        this.viewFormGroup.patchValue(
          { group: normalized },
          { emitEvent: true }
        )
        // Ensure form validation state is updated after async patching
        this.viewFormGroup.updateValueAndValidity({ emitEvent: true })
      })
  }

  /**
   * Loads mapping (client → projects → userGroupIds) for a view.
   * @param {number} viewId - The view ID to fetch the mapping for.
   * @returns { Observable<Record<number,{ projects: Array<Record<string, { userGroupIds: number[] }>> }>>} - The mapping of client IDs to their associated projects and user group IDs.
   */
  private fetchViewClientProjectGroups(
    viewId: number
  ): Observable<
    Record<
      number,
      { projects: Array<Record<string, { userGroupIds: number[] }>> }
    >
  > {
    return this.viewService
      .fetchViewClientProjectGroups$<ResponseModel>(viewId)
      .pipe(
        take(1),
        map(
          (resp) =>
            (resp?.data || {}) as Record<
              number,
              { projects: Array<Record<string, { userGroupIds: number[] }>> }
            >
        )
      )
  }

  /**
   * Patches Client control from mapping and returns resolved IDs for follow-up calls.
   * @param {Record<number, { projects: Array<Record<string, { userGroupIds: number[] }>> }>} mapping - The mapping of client IDs to their associated projects and user group IDs.
   * @returns {Observable<{
   *   mapping: Record<number, unknown>,
   *   clientIds: number[],
   *   selectedProjectIds: number[],
   *   groupIdsByProject: Map<number, number[]>
   * }>} - An observable containing the original mapping, client IDs, selected project IDs, and a map of project IDs to their corresponding group IDs.
   */
  private fetchAndPatchClientsForMapping(
    mapping: Record<number, unknown>
  ): Observable<{
    mapping: Record<number, unknown>
    clientIds: number[]
    selectedProjectIds: number[]
    groupIdsByProject: Map<number, number[]>
  }> {
    const clientIds = Object.keys(mapping)
      .map((k) => Number(k))
      .filter((n) => !isNaN(n))

    const selectedProjectIds: number[] = []
    const groupIdsByProject = new Map<number, number[]>()

    clientIds.forEach((cid) => {
      const entry = mapping[cid]
      ;(entry?.['projects'] || []).forEach(
        (proj: Record<string, { userGroupIds: number[] }>) => {
          const pid = Number(Object.keys(proj)[0])
          if (!isNaN(pid)) {
            selectedProjectIds.push(pid)
            const ids = proj[pid]?.userGroupIds || []
            groupIdsByProject.set(pid, ids)
          }
        }
      )
    })

    return this.layoutFacade.fetchClients$<ResponseModel>().pipe(
      take(1),
      map((clientsResp) => {
        const clients = Array.isArray(clientsResp?.data)
          ? (clientsResp.data as ClientRow[])
          : []
        const clientObjects = clients
          .filter((c) => clientIds.includes(c.clientId))
          .map((c) => ({ ...c, parentId: null as number | null }))

        this.viewFormGroup.patchValue(
          { client: clientObjects },
          { emitEvent: true }
        )

        return { mapping, clientIds, selectedProjectIds, groupIdsByProject }
      })
    )
  }

  /**
   * Patches Case control by fetching projects for the mapped clientIds and selecting intended ones.
   * @param {number[]} clientIds - The IDs of the clients to fetch projects for.
   * @param {number[]} selectedProjectIds - The IDs of the projects to be selected.
   * @returns {Observable<void>} - An observable that completes when the projects have been fetched and the form patched.
   */
  private fetchAndPatchProjectsForMapping(
    clientIds: number[],
    selectedProjectIds: number[]
  ): Observable<void> {
    if (!clientIds?.length) {
      this.viewFormGroup.patchValue({ case: [] }, { emitEvent: true })
      return of(void 0)
    }
    return this.layoutFacade
      .fetchCasesByClientIds<ProjectInfo[]>({ clientIds })
      .pipe(
        take(1),
        map((projects) => {
          const projectObjects = (projects || [])
            .filter((p) => selectedProjectIds.includes(p.projectId))
            .map((p) => ({ ...p, parentId: null as number | null }))
          this.viewFormGroup.patchValue(
            { case: projectObjects },
            { emitEvent: true }
          )
        })
      )
  }

  /**
   * Patches Group control by fetching groups for selected projects and filtering by mapping.
   * @param {number[]} selectedProjectIds - The IDs of the selected projects.
   * @param {Map<number, number[]>} groupIdsByProject - A map of project IDs to their corresponding group IDs.
   * @returns {Observable<{
   *   groupsResp: ResponseModel,
   *   groupIdsByProject: Map<number, number[]>
   * }>} - An observable containing the response with groups and the original map of project IDs to group IDs.
   */
  private fetchAndPatchGroupsForMapping(
    selectedProjectIds: number[],
    groupIdsByProject: Map<number, number[]>
  ): Observable<{
    groupsResp: ResponseModel
    groupIdsByProject: Map<number, number[]>
  }> {
    if (!selectedProjectIds.length) {
      this.viewFormGroup.patchValue({ group: [] }, { emitEvent: true })
      return of(void 0)
    }
    return this.layoutFacade
      .fetchProjectUserGroups$<ResponseModel>(selectedProjectIds)
      .pipe(
        take(1),
        map((groupsResp) => ({ groupsResp, groupIdsByProject }))
      )
  }

  /**
   * Builds the ViewModel payload using form state + cross-tab store snapshot.
   * @returns {ViewModel} - The constructed view payload.
   */
  private buildViewPayload(): ViewModel {
    const formValue = this.viewFormGroup.value

    const selectedProjects = (formValue.case ?? []) as Array<{
      projectId: number
    }>
    const selectedGroups = (formValue.group ?? []) as Array<{ groupId: number }>

    const viewProjectUserGroups: ViewProjectOrUserGroup[] =
      selectedProjects.map((p) => ({
        projectId: p.projectId,
        userGroupIds: [...new Set(selectedGroups.map((g) => g.groupId))],
      }))

    const current = this.currentView()
    const formData: Partial<ViewModel> = current ?? {}

    const viewFields: ViewField[] = (this.selectedFields() || []).map(
      (item, index) => ({
        ...item,
        fieldOrder: index + 1,
      })
    )

    const viewExpression = (
      formData?.viewExpression || 'INTERNAL_FILE_ID>0'
    ).trim()
    const viewExpressionJson = formData?.viewExpressionJson || '[]'
    const viewSortSettings: ViewSortSetting[] = Array.isArray(
      formData.viewSortSettings
    )
      ? formData.viewSortSettings
      : []

    const selectedProjectIds = (selectedProjects || []).map((c) => c.projectId)
    const viewProjectIds = selectedProjectIds?.length
      ? selectedProjectIds
      : this.routeProjectId
      ? [this.routeProjectId]
      : []

    const isPrivate = formValue.isPrivate === true
    const accessibility = isPrivate
      ? Accessibility.Private
      : Accessibility.Public
    const viewUserIds: number[] = isPrivate ? [] : [-1]

    const base: ViewModel = {
      viewId: Number(formData.viewId ?? 0) || 0,
      viewName: formValue.viewName,
      viewType: ViewType.Document,
      isDefault: false,
      viewUserIds,
      accessibility,
      viewFields,
      viewExpression,
      viewExpressionJson,
      viewSortSettings,
      viewProjectIds,
      createdBy: formData?.createdBy,
    }

    return {
      ...(base as unknown as Record<string, unknown>),
      ...(viewProjectUserGroups?.length ? { viewProjectUserGroups } : {}),
    } as unknown as ViewModel
  }

  /**
   * Persists the view (create/update) and coordinates post-save flows.
   * Ensures current user is included for private views.
   * @param {ViewModel} payload - The view payload to persist.
   * @returns {void}
   */
  private persistView(payload: ViewModel): void {
    this.userFacade.selectCurrentUserDetails$
      .pipe(take(1), takeUntil(this.destroy$))
      .subscribe((user) => {
        const isPublic = payload.accessibility === Accessibility.Public
        if (!isPublic) {
          const currentId = user?.userId
          if (currentId && currentId > 0) {
            const set = new Set<number>(payload.viewUserIds || [])
            set.add(currentId)
            payload.viewUserIds = Array.from(set)
          }
        }

        this.documentViewFacade.addOrUpdateView(
          payload as unknown as ViewModel,
          false
        )

        this.monitorSaveResponses(false)
      })
  }

  /**
   * Observes save response streams to present messages and optionally apply the view.
   * @param {boolean} shouldApply - Whether to auto-apply the view after save.
   * @returns {void}
   */
  private monitorSaveResponses(shouldApply: boolean): void {
    const success$ =
      this.documentViewFacade.selectAddOrUpdateViewSuccessResponse$
    const error$ = this.documentViewFacade.selectAddOrUpdateViewErrorResponse$
    const loading$ = this.documentViewFacade.selectIsViewAddOrUpdateLoading$

    combineLatest([success$, error$, loading$])
      .pipe(
        filter(
          ([success, error, isLoading]) =>
            !isLoading && (Boolean(success) || Boolean(error))
        ),
        take(1),
        takeUntil(this.destroy$)
      )
      .subscribe(([success, error]) =>
        this.onSaveResponse(shouldApply, success, error)
      )
  }

  /**
   * Handles a single save response emission (success or error).
   * Updates saving state, shows toast, applies view if needed, and closes dialog.
   * @param {boolean} shouldApply - Whether to auto-apply the view after save.
   * @param {ResponseModel} success - Success response object.
   * @param {ResponseModel} error - Error response object.
   * @returns {void}
   */
  private onSaveResponse(
    shouldApply: boolean,
    success: ResponseModel,
    error: ResponseModel
  ): void {
    this.isSaving.set(false)

    const isError = Boolean(error?.status)
    const message = (success?.message || error?.message || '').trim()
    if (message) this.toast(message, isError ? 'error' : 'success')

    if (!isError && success?.data) {
      // Do not auto-apply view after save anymore per new requirement
      this.windowRef?.close()
    }
  }

  /**
   * Post-success behavior (apply view if needed, refresh defaults, reset search inputs).
   * @returns {void}
   */
  private afterSuccessfulSave(): void {
    if (this.routeProjectId)
      this.viewFacade.fetchUserDefaultView(this.routeProjectId)
    this.viewFacade.isViewManuallyChanged.set(true)
    this.searchFacade.resetSearchInputControls()
  }

  /**
   * Marks all controls in a form group as touched to surface validation errors.
   * @param {FormGroup} fg - The FormGroup to mark.
   * @returns {void}
   */
  private markAllTouched(fg: FormGroup): void {
    Object.keys(fg.controls).forEach((key) => fg.get(key)?.markAsTouched())
  }

  /**
   * Shows a Kendo notification toast.
   * @param {string} content - Message content.
   * @param {'success'|'error'} style - Notification style.
   * @returns {void}
   */
  private toast(content: string, style: 'success' | 'error'): void {
    if (!content?.trim()) return
    const type: Type =
      style === 'error'
        ? ({ style: 'error' } as Type)
        : ({ style: 'success' } as Type)
    const ref = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
      width: 300,
    })
    ref.notification.location.nativeElement.onclick = (): void =>
      ref.hide() as void
  }
}
