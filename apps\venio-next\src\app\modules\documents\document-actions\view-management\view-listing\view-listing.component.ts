import {
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
  computed,
  inject,
  signal,
  NgZone,
  effect,
} from '@angular/core'

import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { GridModule, GridComponent } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { IconsModule } from '@progress/kendo-angular-icons'
import { TooltipModule, PopoverModule } from '@progress/kendo-angular-tooltip'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  DialogsModule,
  WindowModule,
  WindowService,
} from '@progress/kendo-angular-dialog'
import {
  SvgLoaderDirective,
  DynamicHeightDirective,
} from '@venio/feature/shared/directives'
import { DocumentViewFacade } from '@venio/data-access/common'
import {
  ViewModel,
  StartupsFacade,
  UserRights,
} from '@venio/data-access/review'
import { Subject, filter, takeUntil, take } from 'rxjs'
import { ConfirmationDialogService } from '../../../../../services/confirmation-dialog-service'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { toSignal } from '@angular/core/rxjs-interop'

import { ViewManagementContainerComponent } from '../view-management-container/view-management-container.component'

@Component({
  selector: 'venio-view-listing',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    GridModule,
    InputsModule,
    IconsModule,
    TooltipModule,
    PopoverModule,
    LoaderModule,
    SvgLoaderDirective,
    DynamicHeightDirective,
    DialogsModule,
    WindowModule,
  ],
  templateUrl: './view-listing.component.html',
  styleUrl: './view-listing.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ViewListingComponent implements OnInit, OnDestroy {
  /** If true, open directly in create form (set programmatically) */
  public startInCreateMode = false

  /** Currently selected rows (by viewId) */
  public selectedViewIds = signal<number[]>([])

  /** Grid search term */
  public searchTerm = signal('')

  /** Actions enum for template */
  public commonActionTypes = CommonActionTypes

  /** Current action context */
  private selectedAction = signal<CommonActionTypes | null>(null)

  private selectedView = signal<ViewModel | null>(null)

  /** Store subscriptions */
  private readonly toDestroy$ = new Subject<void>()

  private readonly windowService = inject(WindowService)

  private readonly confirmationDialog = inject(ConfirmationDialogService)

  private readonly documentViewFacade = inject(DocumentViewFacade)

  private readonly startupsFacade = inject(StartupsFacade)

  private readonly viewContainerRef = inject(ViewContainerRef)

  private readonly ngZone = inject(NgZone)

  @ViewChild('windowTitleBar', { static: true })
  private windowTitleBar: TemplateRef<unknown>

  @ViewChild('grid', { static: false })
  private grid: GridComponent

  @ViewChild('dialogContainer', { read: ViewContainerRef })
  public dialogContainer: ViewContainerRef

  /** Raw list from store */
  private readonly viewListResponse = toSignal(
    this.documentViewFacade.selectDocumentViewListSuccessResponse$,
    { initialValue: undefined }
  )

  /** Loading flag from store */
  public readonly isLoading = toSignal(
    this.documentViewFacade.selectIsDocumentViewListLoading$,
    { initialValue: false }
  )

  /** Global manage permission */
  public readonly hasViewManageRight = toSignal(
    this.startupsFacade.hasGlobalRight$(
      UserRights.ALLOW_TO_MANAGE_DOCUMENT_VIEW
    ),
    { initialValue: false }
  )

  /** Computed list filtered by search */
  public readonly filteredViews = computed(() => {
    const data: ViewModel[] = (
      (this.viewListResponse()?.data as ViewModel[]) || []
    ).filter((v) => !v.isDefault)
    const term = (this.searchTerm() || '').trim().toLowerCase()
    if (!term) return data
    return data.filter((v) => (v.viewName || '').toLowerCase().includes(term))
  })

  constructor() {
    let timeout = null
    effect(() => {
      const viewList = this.filteredViews()
      if (!viewList?.[0]) return
      if (timeout) clearTimeout(timeout)

      timeout = setTimeout(() => this.fitColumns(), 500)
    })
  }

  public ngOnInit(): void {
    this.documentViewFacade.fetchDocumentViewList()

    if (this.startInCreateMode) {
      // Ensure form opens on first paint, defer to allow list load
      setTimeout(() => this.openCreateForm())
    }
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public fitColumns(): void {
    if (!this.grid) return

    this.ngZone.onStable
      .asObservable()
      .pipe(take(1))
      .subscribe(() => {
        // Auto-fit only the # column (first column)
        const hashColumn = this.grid.columns.find((col) => col.title === '#')
        if (hashColumn) {
          this.grid.autoFitColumn(hashColumn)
        }
      })
  }

  // Toolbar handlers
  public onSearchChange(value: string): void {
    this.searchTerm.set(value || '')
  }

  public onDeleteSelected(): void {
    if (!this.selectedViewIds().length) return
    const title = 'Delete View'
    const content = 'Are you sure you want to delete the selected view(s)?'
    this.confirmationDialog
      .showConfirmationDialog(title, content, this.dialogContainer)
      .pipe(filter(Boolean), take(1))
      .subscribe(() => {
        // Bulk delete using API; effects will refresh list
        const ids = this.selectedViewIds()
        if (ids?.length) {
          this.documentViewFacade.deleteDocumentViews(ids)
        }
        this.selectedViewIds.set([])
      })
  }

  public openCreateForm(): void {
    // Reset any previously selected view context
    this.selectedAction.set(CommonActionTypes.CREATE)
    this.selectedView.set(null)
    this.documentViewFacade.resetDocumentViewState([
      'selectedDocumentView',
      'currentFormData',
    ])
    this.#openViewFormDialog()
  }

  // Grid actions
  public onRowAction(view: ViewModel, action: CommonActionTypes): void {
    // Guard actions by permission + allowToEdit
    const canManage = this.canManageRow(view)
    if (!canManage) return
    if (action === CommonActionTypes.EDIT) {
      this.selectedAction.set(CommonActionTypes.EDIT)
      this.selectedView.set(view)
      // Mirror dropdown behavior to prefill toolbar and mapping
      this.documentViewFacade.storeSelectedDocumentView(view)
      this.documentViewFacade.fetchViewByViewId(view.viewId)
      this.#openViewFormDialog()
      return
    }

    if (action === CommonActionTypes.CLONE) {
      this.selectedAction.set(CommonActionTypes.CLONE)
      this.selectedView.set(view)
      // Prefill toolbar mapping from selected view
      this.documentViewFacade.storeSelectedDocumentView(view)
      // Fetch -> then mutate store for clone (viewId=0, tweak name) and show form
      this.documentViewFacade.fetchViewByViewId(view.viewId)
      this.documentViewFacade.selectCurrentFormData$
        .pipe(filter(Boolean), take(1), takeUntil(this.toDestroy$))
        .subscribe((form) => {
          const clonedName = `${form.viewName}-clone`
          this.documentViewFacade.storeCurrentFormData({
            ...form,
            viewId: 0,
            viewName: clonedName,
          })
          this.#openViewFormDialog()
        })
      return
    }

    if (action === CommonActionTypes.DELETE) {
      const title = 'Delete View'
      const content = `Are you sure you want to delete this view - "${
        view.viewName || ''
      }"?`
      this.confirmationDialog
        .showConfirmationDialog(title, content, this.dialogContainer)
        .pipe(filter(Boolean), take(1))
        .subscribe(() =>
          this.documentViewFacade.deleteDocumentView(view.viewId)
        )
      return
    }
  }

  #openViewFormDialog(): void {
    const ref = this.windowService.open({
      content: ViewManagementContainerComponent,
      state: 'maximized',
      resizable: false,
      draggable: false,
      cssClass: 'v-custom-window',
      title: 'View',
      titleBarContent: this.windowTitleBar,
      appendTo: this.viewContainerRef,
    })
    // Invoked from listing — show Back and return to listing on close/save/cancel
    ref.content.instance.fromListing = true
  }

  /**
   * Whether row actions and selection should be enabled for a row
   * @param view The row's view model
   * @returns boolean
   */
  public canManageRow = (view: ViewModel): boolean => {
    return !!view?.allowToEdit && !!this.hasViewManageRight()
  }

  /**
   * Disable selection for default views or non-manageable rows
   * @param view The row's view model
   * @returns boolean
   */
  public isSelectionDisabled = (view: ViewModel): boolean => {
    return !!view?.isDefault || !this.canManageRow(view)
  }

  /**
   * Sanitize selected keys when user toggles selection
   * @param keys Selected view ids
   * @returns void
   */
  public onSelectedKeysChange = (keys: number[]): void => {
    const viewMap = new Map<number, ViewModel>()
    for (const v of this.filteredViews() || []) viewMap.set(v.viewId, v)
    const sanitized = (keys || []).filter((id) => {
      const v = viewMap.get(id)
      return v ? !this.isSelectionDisabled(v) : false
    })
    this.selectedViewIds.set(sanitized)
  }

  /**
   * Handles checkbox toggle in selection column.
   * Ensures disabled rows cannot be selected and updates selected keys safely.
   * @param view The row's view model
   * @param event The DOM event from input change
   * @returns void
   */
  public onToggleRowSelection(view: ViewModel, event: Event): void {
    if (this.isSelectionDisabled(view)) return
    const target = event.target as HTMLInputElement
    const current = this.selectedViewIds() || []
    if (target?.checked) {
      if (!current.includes(view.viewId)) {
        this.selectedViewIds.set([...current, view.viewId])
      }
    } else {
      this.selectedViewIds.set(current.filter((id) => id !== view.viewId))
    }
  }
}
