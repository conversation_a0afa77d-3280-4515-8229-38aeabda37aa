import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentEditContainerComponent } from './document-edit-container.component'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { DataAccessCommonModule } from '@venio/data-access/common'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideRouter } from '@angular/router'
import { VenioNotificationService } from '@venio/feature/notification'

describe('DocumentEditContainerComponent', () => {
  let component: DocumentEditContainerComponent
  let fixture: ComponentFixture<DocumentEditContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        DocumentEditContainerComponent,
        DataAccessCommonModule,
        // if we need to have a root level object resolved before mock, we do this
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideRouter([]),
        {
          provide: VenioNotificationService,
          useValue: {
            showSuccess: jest.fn(),
            showError: jest.fn(),
            showWarning: jest.fn(),
          },
        },
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        provideMockStore({}),
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentEditContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
