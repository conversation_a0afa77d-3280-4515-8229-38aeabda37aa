variables:
  NX_HEAD: $CI_COMMIT_SHA
  GIT_CLEAN_FLAGS: none
  FF_USE_FASTZIP: 'true'
  ARTIFACT_COMPRESSION_LEVEL: 'fastest'
  CACHE_COMPRESSION_LEVEL: 'fastest'
  TRANSFER_METER_FREQUENCY: '1s'
  CYPRESS_CACHE_FOLDER: '$CI_PROJECT_DIR/.cypress/cache'
  CI: 'true'
  INSTALL_FRESH_NPM: 'false'
  NODE_OPTIONS: '--max_old_space_size=16384'

workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - when: always

stages:
  - prepare
  - dependencies
  - lint
  - test
  - build
  - deploy

cache:
  # untracked: true
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
    - .nx/
    - .angular/

.prepare_script: &prepare_script
  - |
    # Check if nvm is installed
    if (!(Get-Command nvm -ErrorAction SilentlyContinue)) {
      Write-Output "NVM is not installed. Installing NVM..."

      # Define the URL for the latest NVM for Windows release
      $nvmReleaseUrl = "https://github.com/coreybutler/nvm-windows/releases/latest/download/nvm-setup.exe"

      # Define the local path to save the executable
      $nvmSetupPath = "nvm-setup.exe"

      # Download the NVM setup executable
      Invoke-WebRequest -Uri $nvmReleaseUrl -OutFile $nvmSetupPath

      # Run the setup executable
      Start-Process -FilePath $nvmSetupPath -ArgumentList "/S"

      # Refresh the environment to include nvm
      $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine")
    }

    # PowerShell Script to Manage Node.js Version using NVM

    # Function to log errors
    function Log-Error {
        param (
            [String]$message
        )
        Write-Error "Error: $message"
    }

    $desiredVersion = 20.14

    try {
    # Check the current Node.js version
    $currentVersion = & nvm current
    if ($currentVersion -like "*$desiredVersion*") {
        Write-Host "Current Node.js version is already $desiredVersion."
    } else {
        # Check if desired version is installed
        $installedVersions = & nvm list
        if ($installedVersions -like "* $desiredVersion.*") {
            Write-Host "Node.js version $desiredVersion is already installed."
        } else {
            Write-Host "Installing Node.js version $desiredVersion LTS..."
            & nvm install $desiredVersion --lts
            # if we have installed we again update it
            $installedVersions = & nvm list
        }

        # Extract version numbers and find the highest version matching the desired major version
        $versions = $installedVersions -split '\r?\n' | ForEach-Object {
            if ($_ -match "^\s*(\d+\.\d+\.\d+)\s*(\(Currently using.*\))?") {
                $version = $matches[1].Trim()
                if ($version -like "$desiredVersion.*") {
                    [PSCustomObject]@{ Version = [version]$version; Original = $version }
                }
            }
        }

        # Sort the versions and select the highest one
        $versionToUse = $versions | Sort-Object Version -Descending | Select-Object -ExpandProperty Original -First 1

        if (-not $versionToUse) {
            throw "Failed to find or install Node.js version $desiredVersion."
        }

        # Use the desired version
        & nvm use $versionToUse
        Write-Host "Switched to Node.js version $versionToUse."
      }
    } catch {
      Log-Error $_.Exception.Message
    }

    $files = & git diff --name-only $env:NX_BASE HEAD --diff-filter=ADMR

    $commitMsg = & git log --format=%B -n 1 HEAD

    # Check for package-lock.json changes
    $lockfileCheck = ($files -like '*package-lock*')
    Write-Output "Lockfile change check: $lockfileCheck"

    # Check for node_modules
    $nodeModulesCheck = !(Test-Path 'node_modules/.bin')
    Write-Output "Node modules bin check: $nodeModulesCheck"

    # Check commit message
    $commitMsgCheck = ($commitMsg -like '*install-deps*')
    Write-Output "Commit message check: $commitMsgCheck"

    # Check INSTALL_FRESH_NPM flag
    $freshCheck = if ($env:INSTALL_FRESH_NPM -eq 'true') { $true } else { $false }
    Write-Output "Fresh install check: $freshCheck"

    # Join conditions into single variable
    $condition = ($lockfileCheck -or $nodeModulesCheck -or $commitMsgCheck -or $freshCheck)
    Write-Output "Overall condition: $condition"

    $MACHINE_ID = $(hostname)
    $state_file = "${MACHINE_ID}_npm_install_state.json"
    $currentLockfileHash = Get-FileHash -Path 'package-lock.json' -Algorithm SHA256
    $forceFreshInstall = $env:INSTALL_FRESH_NPM -eq 'true'
    $binFolderExists = Test-Path 'node_modules/.bin'

    # Define the Get-InstallState function
    function Get-InstallState {
      param (
        [ String ]$filePath
      )
        if (Test-Path $filePath) {
        $content = Get-Content $filePath | ConvertFrom-Json
        $state = @{}
        $content.PSObject.Properties | ForEach-Object { $state[$_.Name] = $_.Value }
      } else {
        $state = @{ "installed" = $false; "lockfileHash" = ""; "machineID" = $MACHINE_ID }
      }
      return $state
    }

    # Define the Set-InstallState function
    function Set-InstallState {
      param (
        [Hashtable]$state,
        [String]$filePath
      )
      $state | ConvertTo-Json | Set-Content $filePath
    }

    $state = Get-InstallState -filePath $state_file

    if (-not $forceFreshInstall -and $state.installed -and $state.lockfileHash -eq $currentLockfileHash.Hash -and $state.machineID -eq $MACHINE_ID -and $binFolderExists) {
        Write-Output "NPM dependencies are up to date on $MACHINE_ID."
    } else {
        try {
            rm -r -fo node_modules/ -ea Ignore
            rm -r -fo .nx/cache -ea Ignore
            rm -r -fo .angular -ea Ignore
            npm cache clear --force
            Write-Output "Cache and node_modules cleared. Installing dependencies..."
            npm ci --fund=false
            $state.installed = $true
            $state.lockfileHash = $currentLockfileHash.Hash
            Write-Output "Dependencies installed successfully on $MACHINE_ID."
        } catch {
            $state.installed = $false
            Write-Error "Failed to install dependencies: $_"
        } finally {
            Set-InstallState -state $state -filePath $state_file
        }
    }

before_script:
  - '$ConfirmPreference = "None"'
  - 'Import-Module $env:ChocolateyInstall\helpers\chocolateyProfile.psm1'
  - 'Set-Item -Path Env:Path -Value "C:\Program Files\nodejs;C:\ProgramData\nvm;$Env:Path"'
  - 'refreshenv'
  - Write-Output "Before script initiated..."
  - |
    # Specific fetch to get the latest tags and branches
    git fetch --no-tags --prune --depth=100 origin +refs/heads/*:refs/remotes/origin/*

    $currentBranch = $CI_COMMIT_BRANCH
    $branchPattern = 'refs/heads/v\d+\.\d+\.\d+\.\d+$'
    $closestBranch = $null
    $closestCommitDate = [DateTime]::MinValue

    Write-Host "Current branch is $currentBranch"

    # Getting list of all remote branches matching the pattern
    $versionedBranches = git ls-remote --heads origin | Where-Object { $_ -match $branchPattern }

    foreach ($branch in $versionedBranches) {
      $branchName = $branch -replace '.*refs/heads/', ''
      $commitDate = git log -1 --format=%cI origin/$branchName
      $commitDateTime = [DateTime]::Parse($commitDate)

      if ($commitDateTime -gt $closestCommitDate) {
        $closestCommitDate = $commitDateTime
        $closestBranch = $branchName
      }
    }

    # Diagnostic output to verify the selected branch
    Write-Host "Closest branch is $closestBranch"

    if ($closestBranch -eq $null) {
      $closestBranch = 'master'
    }

    # Find the merge base between the current branch and the closest branch
    $baseCommit = $(git merge-base "origin/$closestBranch" "origin/$currentBranch")
    if ($baseCommit) {
    Write-Host "Merge base found: $baseCommit"
    } else {
      $baseCommit = "origin/master"
      Write-Host "No merge base found between $closestBranch and $currentBranch"
    }

    $env:NX_BASE = $baseCommit
  - Write-Host "NX_BASE is set to $env:NX_BASE"
  - *prepare_script

.package_install_script: &package_install_script
  - |
    $MACHINE_ID = $(hostname)
    $state_file = "${MACHINE_ID}_npm_install_state.json"
    $currentLockfileHash = Get-FileHash -Path 'package-lock.json' -Algorithm SHA256
    $forceFreshInstall = $env:INSTALL_FRESH_NPM -eq 'true'
    $binFolderExists = Test-Path 'node_modules/.bin'

    # Define the Get-InstallState function
    function Get-InstallState {
      param (
        [ String ]$filePath
      )
        if (Test-Path $filePath) {
        $content = Get-Content $filePath | ConvertFrom-Json
        $state = @{}
        $content.PSObject.Properties | ForEach-Object { $state[$_.Name] = $_.Value }
      } else {
        $state = @{ "installed" = $false; "lockfileHash" = ""; "machineID" = $MACHINE_ID }
      }
      return $state
    }

    # Define the Set-InstallState function
    function Set-InstallState {
      param (
        [Hashtable]$state,
        [String]$filePath
      )
      $state | ConvertTo-Json | Set-Content $filePath
    }

    $state = Get-InstallState -filePath $state_file

    if (-not $forceFreshInstall -and $state.installed -and $state.lockfileHash -eq $currentLockfileHash.Hash -and $state.machineID -eq $MACHINE_ID -and $binFolderExists) {
        Write-Output "NPM dependencies are up to date on $MACHINE_ID."
    } else {
        try {
            rm -r -fo node_modules/ -ea Ignore
            rm -r -fo .nx/cache -ea Ignore
            rm -r -fo .angular -ea Ignore
            npm cache clear --force
            Write-Output "Cache and node_modules cleared. Installing dependencies..."
            npm ci --fund=false
            $state.installed = $true
            $state.lockfileHash = $currentLockfileHash.Hash
            Write-Output "Dependencies installed successfully on $MACHINE_ID."
        } catch {
            $state.installed = $false
            Write-Error "Failed to install dependencies: $_"
        } finally {
            Set-InstallState -state $state -filePath $state_file
        }
    }

# format-check-affected:
#   stage: lint
#   script:
#     - *package_install_script
#     - 'node_modules/.bin/nx format:check --libs-and-apps --cache=true --base=$NX_BASE --head=$NX_HEAD --verbose'
#   artifacts:
#     exclude:
#       - node_modules/**/*
#       - .cypress/**/*
#       - .nx/**/*
#       - .angular/**/*
.skip_on_version_rules: &skip_on_version_rules
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^v\d+\.\d+\.\d+\.\d+$/'
      when: never
    - when: on_success

es-lint-affected:
  stage: lint
  <<: *skip_on_version_rules
  script:
    - *package_install_script
    - 'node_modules/.bin/nx format:check --libs-and-apps --cache=true --base=$NX_BASE --head=$NX_HEAD --verbose'
    - 'node_modules/.bin/nx affected --target=lint --head=$NX_HEAD --base=$NX_BASE --parallel=3 --quiet=true --cache=true'
  artifacts:
    exclude:
      - node_modules/**/*
      - .cypress/**/*
      - .nx/**/*
      - .angular/**/*

# unit-test-affected:
#   stage: test
#   script:
#     - *package_install_script
#     - 'node_modules/.bin/nx affected --target=test --head=$NX_HEAD --base=$NX_BASE --parallel=3 --configuration=ci --colors --code-coverage --with-deps --cache=true'
#   artifacts:
#     expire_in: 1 day
#     paths:
#       - '**/test-reports/jest-junit-report.xml'
#     exclude:
#       - node_modules/**/*
#       - .cypress/**/*
#       - .nx/**/*
#       - .angular/**/*
#     reports:
#       junit:
#         - '**/test-reports/jest-junit-report.xml'

unit-test-full:
  stage: test
  needs:
    # - job: format-check-affected
    - job: es-lint-affected
      optional: true
  <<: *skip_on_version_rules
  script:
    - *package_install_script
    - 'node_modules/.bin/nx run-many --target=test --parallel=3 --configuration=ci --colors'
  artifacts:
    expire_in: 4 day
    paths:
      - '**/test-reports/jest-junit-report.xml'
    exclude:
      - node_modules/**/*
      - .cypress/**/*
      - .nx/**/*
      - .angular/**/*
    reports:
      junit:
        - '**/test-reports/jest-junit-report.xml'

build-app-shell-full:
  stage: build
  needs:
    - job: unit-test-full
      optional: true
  rules:
    - if: '$CI_COMMIT_REF_NAME !~ /^master$/ && $CI_COMMIT_REF_NAME !~ /^v\d+\.\d+\.\d+\.\d+$/'
      when: on_success
    - when: never
  script:
    - *package_install_script
    - 'node_modules/.bin/nx run venio-next:app-shell:production --parallel=4'
  artifacts:
    exclude:
      - node_modules/**/*
      - .cypress/**/*
      - .nx/**/*
      - .angular/**/*

build-venio-next:
  stage: build
  needs:
    - job: unit-test-full
      optional: true
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^master/'
    - if: $CI_COMMIT_BRANCH =~ /^v\d+\.\d+\.\d+\.\d+/
  script:
    - *package_install_script
    # Dynamically add the base href of an angular option before build.
    # Only applicable here as default is none.
    # If we need to have a differently deployed url, we can simply set there or dynamically set here as an update.
    - 'node ./tools/tasks/pass-angular-href-base-option /VenioWeb/OnDemand/venio-next/'
    - 'node ./tools/tasks/set-app-version-on-feature-flag.js --appVersion=$CI_COMMIT_BRANCH --buildNumber=$CI_PIPELINE_ID'
    - 'node_modules/.bin/nx run venio-next:app-shell:production --parallel=6 --skip-nx-cache'
    # - 'node ./tools/tasks/copy-web-config-to-build.js'
    - 'node ./tools/tasks/pass-angular-href-base-option undo'
  artifacts:
    paths:
      - '**/dist/'
      - powershell -command Get-ChildItem -Path dist\venio-next\browser
    exclude:
      - node_modules/**/*
      - .cypress/**/*
      - .nx/**/*
      - .angular/**/*
    expire_in: 5 day
deploy-build-venio-next:
  stage: deploy
  needs:
    - job: build-venio-next
      optional: true
  rules:
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
    - if: $CI_COMMIT_BRANCH =~ /^v\d+\.\d+\.\d+\.\d+/
  before_script:
    - '' # we don't need to run before script stage as we do not use node_modules
  cache: {} # don't use cache here unless, we need to run some cmd from cache
  environment:
    name: testing
    url: https://dev-qc01.veniosystems.com/VenioWeb/OnDemand/AppPlus/#/login
  script:
    - echo "display the available folders"
    - powershell -command Get-ChildItem -Path dist\venio-next\browser\ -Directory
    - echo "remove existing files from the target for same pipeline id"
    - powershell -command Remove-Item \\fs02.ad.veniosystems.com\teamcity\$CI_COMMIT_BRANCH\venio-next\$CI_PIPELINE_ID\venio-next\* -r -fo -ea Ignore
    - echo "copy the files and folders to the target"
    - powershell -command net use \\fs02.ad.veniosystems.com Sequence,Award,Eastbound,V3n!0 /USER:ad\venio_svc
    - powershell -command New-Item -Path \\fs02.ad.veniosystems.com\teamcity\$CI_COMMIT_BRANCH\Venio-Next\$CI_PIPELINE_ID\venio-next -Type Directory
    - powershell -command Copy-Item -Path dist\venio-next\browser\ -Destination \\fs02.ad.veniosystems.com\teamcity\$CI_COMMIT_BRANCH\Venio-Next\$CI_PIPELINE_ID\venio-next -r -fo
    - echo "copy the files and folders to the latest folder in FS02 by clearing the folder"
    - powershell -command Remove-Item \\fs02.ad.veniosystems.com\teamcity\$CI_COMMIT_BRANCH\Venio-Next\latest\venio-next\* -r -fo -ea Ignore
    - powershell -command Copy-Item -Path dist\venio-next\browser\* -Destination \\fs02.ad.veniosystems.com\teamcity\$CI_COMMIT_BRANCH\Venio-Next\latest\venio-next -r -fo
    - powershell -command New-Item -Path \\fs02.ad.veniosystems.com\teamcity\$CI_COMMIT_BRANCH\Venio-Next\latest\venio-next\PipelineID.txt -Value $CI_PIPELINE_ID -ItemType File -Force
    - echo "Copied files to fs02"
    - echo "Trigerring teamcity build"
    - powershell -File tools/tasks/trigger-teamcity-build.ps1 -TeamCityToken $env:TEAMCITY_TOKEN -TeamCityServerUrl $env:TEAMCITY_SERVER_URL -BranchName $env:CI_COMMIT_BRANCH
