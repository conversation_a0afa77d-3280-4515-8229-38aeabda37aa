import { createAction, props } from '@ngrx/store'
import { ViewModel } from '../../models/interfaces/view.model'
import { ViewState } from './view.reducer'

type StateKeys = keyof ViewState

export const resetView = createAction(
  '[View] Reset View',
  props<{
    stateKeys: StateKeys | StateKeys[]
  }>()
)

export const saveUserDefaultView = createAction(
  '[View] Save User Default View',
  props<{
    projectId: number
    viewId: number
  }>()
)

export const fetchUserDefaultView = createAction(
  '[View] Fetch User Default View',
  props<{
    projectId: number
  }>()
)

export const fetchViewById = createAction(
  '[View] Fetch View By Id',
  props<{
    viewId: number
  }>()
)

export const fetchUserDefaultViewSuccess = createAction(
  '[View] Fetch User Default View Success',
  props<{ view: ViewModel }>()
)

export const storeSelectedViewDefaultExpression = createAction(
  '[View] Store Selected View Default Expression',
  props<{ selectedViewDefaultExpression: string }>()
)

export const setUserDefaultView = createAction(
  '[View] Set User Default View',
  props<{ userDefaultView: ViewModel }>()
)
