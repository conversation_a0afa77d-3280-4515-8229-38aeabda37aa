import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  ElementRef,
  HostListener,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
  ViewEncapsulation,
  inject,
  OnDestroy,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { PopupModule } from '@progress/kendo-angular-popup'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import {
  chevronDownIcon,
  pencilIcon,
  plusCircleIcon,
} from '@progress/kendo-svg-icons'
import { ListViewModule } from '@progress/kendo-angular-listview'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { InputsModule, TextBoxComponent } from '@progress/kendo-angular-inputs'
import { CommonActionTypes } from '@venio/shared/models/constants'
import {
  DialogService,
  WindowService,
  WindowRef,
  WindowModule,
} from '@progress/kendo-angular-dialog'
import { DocumentViewFacade, UserFacade } from '@venio/data-access/common'
import { Subject, combineLatest, filter, takeUntil } from 'rxjs'
import {
  FieldFacade,
  SearchFacade,
  StartupsFacade,
  UserRights,
  ViewFacade,
  ViewModel,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { isEqual } from 'lodash'
import { debounceTime } from 'rxjs/operators'
import {
  BreadcrumbFacade,
  BreadcrumbService,
} from '@venio/data-access/breadcrumbs'
import { GroupStackType } from '@venio/shared/models/interfaces'
import { toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'venio-document-view-designer-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    PopupModule,
    ButtonsModule,
    IconsModule,
    ListViewModule,
    LayoutModule,
    InputsModule,
    WindowModule,
  ],
  templateUrl: './document-view-designer-dropdown.component.html',
  styleUrl: './document-view-designer-dropdown.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class DocumentViewDesignerDropdownComponent
  implements AfterViewInit, OnDestroy
{
  private viewContainerRef = inject(ViewContainerRef)

  private dialogService = inject(DialogService)

  private windowService = inject(WindowService)

  private documentViewFacade = inject(DocumentViewFacade)

  private breadcrumbFacade = inject(BreadcrumbFacade)

  private breadcrumbService = inject(BreadcrumbService)

  private reviewViewFacade = inject(ViewFacade)

  private searchFacade = inject(SearchFacade)

  private activatedRoute = inject(ActivatedRoute)

  private fieldFacade = inject(FieldFacade)

  private userFacade = inject(UserFacade)

  private startupFacade = inject(StartupsFacade)

  public toDestroy$ = new Subject<void>()

  public dropdownContentVisibility = false

  public downIcon = chevronDownIcon

  public iconPlus = plusCircleIcon

  public iconPencil = pencilIcon

  public selectedDocumentViewDesignerDropdownItem = signal<ViewModel>(
    {} as ViewModel
  )

  public commonActionTypes = CommonActionTypes

  public loadedItems = signal<ViewModel[]>([])

  public allItems: ViewModel[]

  private currentUser = toSignal(this.userFacade.selectCurrentUserDetails$)

  public isExternalUser = computed((): boolean => {
    const user = this.currentUser()
    return user?.userRole.match(/external/gi) !== null
  })

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  @ViewChild('searchInput')
  private searchInput: TextBoxComponent

  @ViewChild('dropdownAnchor', { read: ElementRef }) public anchor: ElementRef

  @ViewChild('itemPopup', { read: ElementRef }) public popup: ElementRef

  // Title bar template for Kendo Window (consistent with layout)
  @ViewChild('viewWindowTitleBar', { static: true })
  private viewWindowTitleBar: TemplateRef<unknown>

  public ngAfterViewInit(): void {
    this.#fetchDocumentViews()
    this.#selectDocumentViewItems()
    this.#selectUserDefaultView()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Handles keyboard events on the entire document. Closes the dropdown when the Escape key is pressed.
   * @param {KeyboardEvent} event - The keyboard event that was triggered.
   * @returns {void}
   */
  @HostListener('document:keydown', ['$event'])
  public keydown(event: KeyboardEvent): void {
    if (event.code === 'Escape') {
      this.toggleDropdown(false)
    }
  }

  /**
   * Handles click events on the entire document. Closes the dropdown if the click is outside the component.
   * @param {MouseEvent} event - The mouse event that was triggered.
   * @returns {void}
   */
  @HostListener('document:click', ['$event'])
  public documentClick(event: KeyboardEvent): void {
    if (!this.#contains(event.target)) {
      this.toggleDropdown(false)
    }
  }

  /**
   * Updates the view based on the current search term.
   * Filters and displays items matching the search term.
   * @returns {void}
   */
  public updateViewBasedOnSearch(): void {
    const searchTerm = (this.searchInput?.value || '').trim()
    const data = searchTerm
      ? this.#filterItemsBySearchTerm(searchTerm)
      : this.#filterByLoadFlag(this.allItems).slice(0, 20)
    this.loadedItems.set(data)
  }

  /**
   * Adjusts the opacity of the icon container element.
   * @param {HTMLSpanElement} element - The span element that contains the icon.
   * @param {boolean} isEnter - Indicates whether the mouse is entering or leaving the element.
   * @returns {void}
   */
  public toggleIconContainerOpaque(
    element: HTMLSpanElement,
    isEnter: boolean
  ): void {
    element.style.opacity = isEnter ? '1' : '0'
  }

  /**
   * Toggles the visibility of the dropdown content.
   * @param {boolean} [show] - Optional parameter to explicitly set the visibility state.
   * @returns {void}
   */
  public toggleDropdown(show?: boolean): void {
    this.dropdownContentVisibility =
      show !== undefined ? show : !this.dropdownContentVisibility

    if (this.dropdownContentVisibility) {
      setTimeout(() => {
        this.loadedItems.set([])
        this.loadMoreItems()
        this.searchInput.focus()
      }, 0)
    }
  }

  /**
   * Loads more items into the view. If a search term is present, it loads filtered items.
   * @returns {void}
   */
  public loadMoreItems(): void {
    const alreadyLoaded = this.loadedItems()
    const next = alreadyLoaded.length
    const searchTerm = (this.searchInput?.value || '').trim()
    const itemsToLoad = searchTerm
      ? this.#filterItemsBySearchTerm(searchTerm)
      : this.#filterByLoadFlag(this.allItems || [])

    const allExceptSelected = [
      ...alreadyLoaded,
      ...itemsToLoad.slice(next, next + 20),
    ].filter(
      (item) =>
        item.viewId !== this.selectedDocumentViewDesignerDropdownItem()?.viewId
    )

    const uniqueItems = [
      ...new Set(allExceptSelected.map((c) => c.viewId)),
    ].map((id) => allExceptSelected.find((c) => c.viewId === id))

    this.loadedItems.set(uniqueItems)
  }

  /**
   * Handles the action when an item in the list is clicked.
   * @param {ViewModel} item - The item that was clicked.
   * @param {CommonActionTypes} actionType - The type of action to perform.
   * @param {MouseEvent} event - Current event target to capture and perform the task
   * @returns {void}
   */
  public listItemClickAction(
    item: ViewModel,
    actionType: CommonActionTypes,
    event: MouseEvent
  ): void {
    this.breadcrumbService.setConditionChecked(GroupStackType.VIEW_SEARCH, true)
    // it is better to clear the current form data when the user clicks on the list item
    this.documentViewFacade.resetDocumentViewState('currentFormData')

    const container = event.currentTarget as HTMLDivElement
    const isListItemContainer = container.classList.contains(
      'v-document-view-item'
    )

    const isSelectable = actionType === CommonActionTypes.SELECT
    const isAdd = actionType === CommonActionTypes.ADD
    const isEditable = actionType === CommonActionTypes.EDIT
    const isDeletable = actionType === CommonActionTypes.DELETE

    if (isSelectable || isDeletable || isEditable) {
      if (isListItemContainer) {
        this.#resetUserDefaultViewState()
        this.selectedDocumentViewDesignerDropdownItem.set(item)
        // save the selected view as the user default view
        this.reviewViewFacade.saveUserDefaultView(this.projectId, item.viewId)

        // The Selected view has changed, so we need to reset the search input.
        this.searchFacade.resetSearchInputControls()
        this.reviewViewFacade.isViewManuallyChanged.set(true)
      }
      this.documentViewFacade.storeSelectedDocumentView(item)
      this.reviewViewFacade.storeSelectedViewDefaultExpression(
        item.viewExpression
      )
    }

    if (actionType === CommonActionTypes.EDIT && !isListItemContainer) {
      // Load the view details in store, then open the new View Management dialog for editing
      this.documentViewFacade.fetchViewByViewId(item.viewId)
    }

    if (!isListItemContainer && (isEditable || isAdd)) {
      if (isAdd) this.#launchViewListingDialog(true)
      else this.#launchViewManagementDialog()
    }

    this.toggleDropdown(false)

    this.loadedItems.set([])
    this.loadMoreItems()
    this.searchFacade.resetSearchState('searchFormValues')

    if (isAdd) {
      this.documentViewFacade.resetDocumentViewState('selectedDocumentView')
    }
  }

  /**
   * Launches the view management dialog.
   * @returns {void}
   */
  #launchViewManagementDialog(): void {
    import(
      '../../view-management/view-management-container/view-management-container.component'
    ).then(({ ViewManagementContainerComponent }) => {
      const ref = this.windowService.open({
        content: ViewManagementContainerComponent,
        state: 'maximized',
        resizable: false,
        draggable: false,
        cssClass: 'v-custom-window',
        title: 'View',
        titleBarContent: this.viewWindowTitleBar,
        appendTo: this.viewContainerRef,
      })
      // Opened directly from dropdown - no listing context
      ref.content.instance.fromListing = false
    })
  }

  /**
   * Handles the "View All" button click to show all available views.
   * @returns {void}
   */
  public viewAllViews(): void {
    this.toggleDropdown(false)
    // Show all loadable views in the dropdown list
    this.loadedItems.set(this.#filterByLoadFlag(this.allItems || []))
    this.#launchViewListingDialog(false)
  }

  #selectUserDefaultView(): void {
    this.reviewViewFacade.selectUserDefaultView$
      .pipe(
        filter(
          (view) =>
            Boolean(view) &&
            !isEqual(view, this.selectedDocumentViewDesignerDropdownItem())
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((view) => {
        // Find the complete view object from allItems to ensure we have all properties including allowToEdit
        const completeView = this.allItems?.find(
          (item) => item.viewId === view.viewId
        )
        // This is necessary as some UI element are depending on this,
        // while user default view from backend may not have all properties,
        // so we need to merge them to avoid breaking the UI.
        this.selectedDocumentViewDesignerDropdownItem.set({
          ...view,
          allowToEdit: completeView?.allowToEdit,
          allowToLoad: completeView?.allowToLoad,
        })
      })
  }

  /**
   * Fetches user selected view
   * @returns {void}
   */
  #fetchUserSelectedView(): void {
    this.reviewViewFacade.fetchUserDefaultView(this.projectId)
  }

  #resetUserDefaultViewState(): void {
    this.reviewViewFacade.resetView('userDefaultView')
  }

  /**
   * Fetches the list of document views.
   * @returns {void}
   */
  #fetchDocumentViews(): void {
    this.documentViewFacade.fetchDocumentViewList()
  }

  /**
   * Sets the default selected view from the list of all items if there aren't already selected
   * or replace with an update version for non-default view.
   * @returns {void}
   */
  #setSelectedDefaultView(): void {
    const selectedView = this.selectedDocumentViewDesignerDropdownItem()
    const found = this.allItems.find((c) => c.viewId === selectedView?.viewId)
    if (selectedView?.viewName && found) {
      // Always use the found item from allItems to ensure we have the complete view object with allowToEdit flag
      this.selectedDocumentViewDesignerDropdownItem.set(found)
    } else {
      // Set default view if no current selection or current selection not found
      const defaultView = this.allItems.find((c) => c.isDefault)
      if (defaultView) {
        this.selectedDocumentViewDesignerDropdownItem.set(defaultView)
      }
    }

    // whatever the view selected, the selected view ID is used to fetch the view for the document table
    this.#fetchUserSelectedView()
  }

  /**
   * Assigns the complete list of document views to the `allItems` property.
   * @param {ViewModel[]} views - Array of ViewModel items representing document views.
   * @returns {void}
   */
  #setAllItems(views: ViewModel[]): void {
    this.allItems = views || []
  }

  /**
   * Subscribes to document view items and handles their selection.
   * @returns {void}
   */
  #selectDocumentViewItems(): void {
    combineLatest([
      this.documentViewFacade.selectDocumentViewListSuccessResponse$,
      this.documentViewFacade.selectDocumentViewListErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success || error)),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        // clear existing items
        this.loadedItems.set([])

        this.#setAllItems(success?.data)
        this.loadMoreItems()
      })
  }

  /**
   * Filters the items based on the provided search term.
   * @param {string} searchTerm - The term used for filtering items.
   * @returns {ViewModel[]} - Filtered array of items.
   */
  #filterItemsBySearchTerm(searchTerm: string): ViewModel[] {
    return this.#filterByLoadFlag(this.allItems).filter(
      (item) =>
        item.viewName.toLowerCase().includes(searchTerm.toLowerCase()) &&
        item.viewId !== this.selectedDocumentViewDesignerDropdownItem()?.viewId
    )
  }

  /**
   * Filters items by per-item allowToLoad flag. Defaults to allowed when flag is undefined.
   */
  #filterByLoadFlag(items: ViewModel[]): ViewModel[] {
    return (items || []).filter((it) => it?.allowToLoad)
  }

  /**
   * Checks if the target element is contained within the component.
   * @param {Node} target - The target element to check.
   * @returns {boolean} - True if the target is within the component, false otherwise.
   */
  #contains(target: EventTarget): boolean {
    return (this.anchor.nativeElement.contains(target) ||
      (this.popup
        ? this.popup.nativeElement.contains(target)
        : false)) as boolean
  }

  /**
   * Launches the document view designer dialog.
   * @param {CommonActionTypes} actionType - The type of action to be performed in the dialog.
   * @returns {void}
   */
  #launchDocumentViewDesignerDialog(actionType: CommonActionTypes): void {
    import(
      '../document-view-designer-dialog-container/document-view-designer-dialog-container.component'
    ).then(({ DocumentViewDesignerDialogContainerComponent }) => {
      this.fieldFacade.fetchAllCustomFields(this.projectId, true)

      this.dialogService.open({
        title: `${actionType === CommonActionTypes.ADD ? 'New' : 'Edit'} View`,
        content: DocumentViewDesignerDialogContainerComponent,
        maxWidth: '90vw',
        minWidth: '90vw',
        minHeight: '90vh',
        maxHeight: '90vh',
        appendTo: this.viewContainerRef,
      })
    })
  }

  /**
   * Opens the View Listing dialog with optional initial create mode.
   * @param {boolean} startInCreateMode - Whether the dialog should start with the Create View form.
   * @returns {void}
   */
  #launchViewListingDialog(startInCreateMode = false): void {
    import('../../view-management/view-listing/view-listing.component').then(
      ({ ViewListingComponent }) => {
        const ref: WindowRef = this.windowService.open({
          content: ViewListingComponent,
          state: 'maximized',
          resizable: false,
          draggable: false,
          cssClass: 'v-custom-window',
          title: 'View',
          // left: 0,
          // top: -250,
          titleBarContent: this.viewWindowTitleBar,
          appendTo: this.viewContainerRef,
        })
        ref.content.instance.startInCreateMode = startInCreateMode
      }
    )
  }

  public hasViewManageRight = toSignal(
    this.startupFacade.hasGlobalRight$(UserRights.ALLOW_TO_MANAGE_DOCUMENT_VIEW)
  )
}
