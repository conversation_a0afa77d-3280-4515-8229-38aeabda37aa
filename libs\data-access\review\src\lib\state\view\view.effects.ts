import { Injectable } from '@angular/core'
import * as fromViewActions from './view.actions'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { EMPTY, filter, map, switchMap } from 'rxjs'
import { ViewService } from '../../services/view.service'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { fetch } from '@ngrx/router-store/data-persistence'

@Injectable()
export class ViewEffects {
  constructor(private actions$: Actions, private viewService: ViewService) {}

  public saveUserDefaultView$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromViewActions.saveUserDefaultView),
      fetch({
        run: ({ projectId, viewId }) => {
          return this.viewService.saveUserDefaultView(projectId, viewId).pipe(
            switchMap(() => this.viewService.fetchUserDefaultView(projectId)),
            map((response: ResponseModel) => {
              return fromViewActions.fetchUserDefaultViewSuccess({
                view: response.data,
              })
            })
          )
        },
        onError: (action, error) => {
          return EMPTY
        },
      })
    )
  )

  public fetchUserDefaultView$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromViewActions.fetchUserDefaultView),
      filter(({ projectId }) => projectId > 0),
      fetch({
        run: ({ projectId }) => {
          return this.viewService.fetchUserDefaultView(projectId).pipe(
            map((response: ResponseModel) => {
              return fromViewActions.fetchUserDefaultViewSuccess({
                view: response.data,
              })
            })
          )
        },
        onError: (action, error) => {
          return EMPTY
        },
      })
    )
  )

  public fetchViewById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromViewActions.fetchViewById),
      filter(({ viewId }) => viewId > 0),
      fetch({
        run: ({ viewId }) => {
          return this.viewService.fetchViewById(viewId).pipe(
            map((response: ResponseModel) => {
              return fromViewActions.fetchUserDefaultViewSuccess({
                view: response.data,
              })
            })
          )
        },
        onError: (action, error) => {
          return EMPTY
        },
      })
    )
  )
}
