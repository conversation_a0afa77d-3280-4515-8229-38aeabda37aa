import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReactiveFormsModule } from '@angular/forms'
import { provideHttpClient } from '@angular/common/http'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { WindowRef } from '@progress/kendo-angular-dialog'
import { of } from 'rxjs'
import { provideMockStore } from '@ngrx/store/testing'
import { ViewManagementContainerComponent } from './view-management-container.component'
import {
  FieldFacade,
  CompositeLayoutFacade,
  FieldService,
  ViewFacade,
  SearchFacade,
  ViewService,
} from '@venio/data-access/review'
import { DocumentViewFacade, UserFacade } from '@venio/data-access/common'
import { ActivatedRoute } from '@angular/router'
import { NotificationService } from '@progress/kendo-angular-notification'

describe('ViewManagementContainerComponent', () => {
  let component: ViewManagementContainerComponent
  let fixture: ComponentFixture<ViewManagementContainerComponent>

  // Mocks
  let mockWindowRef: any
  let mockFieldFacade: any
  let mockUserFacade: any
  let mockLayoutFacade: any
  let mockFieldService: any
  let mockDocumentViewFacade: any
  let mockNotificationService: any
  let mockViewFacade: any
  let mockSearchFacade: any
  let mockViewService: any

  beforeEach(async () => {
    // GIVEN the window ref mock
    mockWindowRef = {
      close: jest.fn(),
    }

    // GIVEN the field facade mock
    mockFieldFacade = {
      fetchAllPermittedFields: jest.fn(),
      fetchAllVenioFields: jest.fn(),
      selectAllVenioFields$: of([]), // Add the missing property
    }

    // GIVEN the user facade mock
    mockUserFacade = {
      fetchUserList: jest.fn(),
      selectCurrentUserDetails$: of({ userId: 1, username: 'testuser' }),
    }

    // GIVEN the layout facade mock
    mockLayoutFacade = {
      fetchCasesByClientIds: jest.fn().mockReturnValue({
        pipe: () => ({ subscribe: (): void => {} }),
      }),
      fetchClients$: jest.fn().mockReturnValue(of({ data: [] })),
      fetchProjectUserGroups$: jest.fn().mockReturnValue(of({ data: [] })),
    }

    // GIVEN the field service mock
    mockFieldService = {
      fetchAllCustomFields$: jest.fn().mockReturnValue({
        pipe: () => ({ subscribe: (): void => {} }),
      }),
    }

    // GIVEN the document view facade mock
    mockDocumentViewFacade = {
      selectCurrentFormData$: of({}),
      storeCurrentFormData: jest.fn(),
      addOrUpdateView: jest.fn(),
      selectAddOrUpdateViewSuccessResponse$: of(null),
      selectAddOrUpdateViewErrorResponse$: of(null),
      selectIsViewAddOrUpdateLoading$: of(false),
      selectSelectedDocumentView$: of(null),
      fetchSearchFields: jest.fn(), // Add the missing method
    }

    // GIVEN the notification service mock
    mockNotificationService = {
      show: jest.fn().mockReturnValue({
        notification: {
          location: {
            nativeElement: {
              onclick: (): void => {},
            },
          },
        },
        hide: (): void => {},
      }),
    }

    // GIVEN the view facade mock
    mockViewFacade = {
      isViewManuallyChanged: { set: jest.fn() },
      fetchUserDefaultView: jest.fn(),
    }

    // GIVEN the search facade mock
    mockSearchFacade = {
      resetSearchInputControls: jest.fn(),
    }

    // GIVEN the view service mock
    mockViewService = {
      fetchViewClientProjectGroups$: jest
        .fn()
        .mockReturnValue(of({ data: {} })),
    }

    await TestBed.configureTestingModule({
      imports: [ViewManagementContainerComponent, ReactiveFormsModule],
      providers: [
        { provide: WindowRef, useValue: mockWindowRef },
        { provide: FieldFacade, useValue: mockFieldFacade },
        { provide: UserFacade, useValue: mockUserFacade },
        { provide: CompositeLayoutFacade, useValue: mockLayoutFacade },
        { provide: FieldService, useValue: mockFieldService },
        { provide: DocumentViewFacade, useValue: mockDocumentViewFacade },
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: ViewFacade, useValue: mockViewFacade },
        { provide: SearchFacade, useValue: mockSearchFacade },
        { provide: ViewService, useValue: mockViewService },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: { projectId: 0 } },
          },
        } as unknown as ActivatedRoute,
        provideMockStore(),
        provideHttpClient(),
        provideNoopAnimations(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ViewManagementContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should successfully create the view management container component', () => {
    // GIVEN the component is set up
    // THEN we verify it exists
    expect(component).toBeTruthy()
  })

  it('should initialize form with empty view name, empty client list, and public view by default', () => {
    // GIVEN we start with default form
    // THEN check initial fields
    expect(component.viewFormGroup).toBeDefined()
    expect(component.viewFormGroup.get('viewName')?.value).toBe('')
    expect(component.viewFormGroup.get('client')?.value).toEqual([])
    expect(component.viewFormGroup.get('isPrivate')?.value).toBe(false)
  })

  it('should close the dialog window when the back button is clicked', () => {
    // GIVEN the component is initialized
    // WHEN goBack is called
    component.goBack()

    // THEN the dialog should close
    expect(mockWindowRef.close).toHaveBeenCalled()
  })

  it('should close the dialog window when the cancel button is clicked', () => {
    // GIVEN the component is initialized
    // WHEN cancel is called
    component.cancel()

    // THEN the dialog should close
    expect(mockWindowRef.close).toHaveBeenCalled()
  })

  it('should change the active tab when a different tab is selected', () => {
    // GIVEN the component is initialized
    // WHEN onTabSelect is called with index 1
    component.onTabSelect(1)

    // THEN the active tab should be set to 1
    expect(component.activeTab()).toBe(1)
  })

  it('should return false for form validity when required fields are missing', () => {
    // GIVEN an invalid form with no fields selected
    component.selectedFields.set([])

    // THEN isFormValid should return false
    expect(component.isFormValid).toBe(false)
  })

  it('should return false for form validity when fields are selected but view configuration is incomplete', () => {
    // GIVEN no view name and no selections
    component.viewFormGroup.patchValue({
      viewName: '',
      isPrivate: false,
    })

    // AND no fields selected
    component.selectedFields.set([])

    // THEN isFormValid should return false
    expect(component.isFormValid).toBe(false)
  })

  it('should validate that form is valid when all required fields are provided and at least one field is selected', () => {
    // GIVEN valid form with a view name
    component.viewFormGroup.patchValue({
      viewName: 'Test View',
      isPrivate: false,
    })

    // AND properly set array values
    component.viewFormGroup
      .get('client')
      ?.setValue([{ clientId: 1, clientName: 'Test Client' }])
    component.viewFormGroup
      .get('case')
      ?.setValue([{ projectId: 1, projectName: 'Test Project' }])
    component.viewFormGroup
      .get('group')
      ?.setValue([{ groupId: 1, groupName: 'Test Group' }])

    // AND at least one field is selected
    component.selectedFields.set([
      { fieldId: 1, fieldName: 'Field1', fieldOrder: 1, isCustomField: false },
    ])

    // THEN the form should be valid
    expect(component.viewFormGroup.valid).toBe(true)

    // AND there should be at least one selected field
    expect(component.selectedFields().length).toBeGreaterThan(0)
  })

  it('should mark form fields as touched but not save when form is invalid', () => {
    // GIVEN an invalid form
    // WHEN save is called
    component.save()

    // THEN form controls should be marked as touched
    const anyTouched = Object.values(component.viewFormGroup.controls).some(
      (c): boolean => c.touched
    )
    expect(anyTouched).toBe(true)

    // AND isSaving should remain false
    expect(component.isSaving()).toBe(false)
  })
})
