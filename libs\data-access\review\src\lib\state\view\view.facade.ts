import { Injectable, signal, WritableSignal } from '@angular/core'
import { Store, select } from '@ngrx/store'
import * as fromViewActions from './view.actions'
import * as ViewSelectors from './view.selectors'
import { ViewState } from './view.reducer'
import { ViewModel } from '../../models/interfaces/view.model'

@Injectable({ providedIn: 'root' })
export class ViewFacade {
  constructor(private readonly store: Store) {}

  public isViewManuallyChanged: WritableSignal<boolean> = signal(false)

  public saveUserDefaultView(projectId: number, viewId: number): void {
    this.store.dispatch(
      fromViewActions.saveUserDefaultView({ projectId, viewId })
    )
  }

  public fetchUserDefaultView(projectId: number): void {
    this.store.dispatch(fromViewActions.fetchUserDefaultView({ projectId }))
  }

  public fetchViewById(viewId: number): void {
    this.store.dispatch(fromViewActions.fetchViewById({ viewId }))
  }

  public storeSelectedViewDefaultExpression(
    selectedViewDefaultExpression: string
  ): void {
    this.store.dispatch(
      fromViewActions.storeSelectedViewDefaultExpression({
        selectedViewDefaultExpression,
      })
    )
  }

  public setUserDefaultView(userDefaultView: ViewModel): void {
    this.store.dispatch(fromViewActions.setUserDefaultView({ userDefaultView }))
  }

  public resetView(stateKeys: keyof ViewState | Array<keyof ViewState>): void {
    this.store.dispatch(fromViewActions.resetView({ stateKeys }))
  }

  public selectUserDefaultView$ = this.store.pipe(
    select(ViewSelectors.getStateOfViewState('userDefaultView'))
  )

  public selectSelectedViewDefaultExpression$ = this.store.pipe(
    select(ViewSelectors.getStateOfViewState('selectedViewDefaultExpression'))
  )

  public getSelectedViewFields$ = this.store.pipe(
    select(ViewSelectors.getViewFields)
  )

  public selectIsDocumentTableViewLoading$ = this.store.pipe(
    select(ViewSelectors.getStateOfViewState('isDocumentTableViewLoading'))
  )

  public selectIsUserDefaultViewLoading$ = this.store.pipe(
    select(ViewSelectors.getStateOfViewState('isUserDefaultViewLoading'))
  )
}
