import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as DocumentViewActions from './document-view.actions'
import * as DocumentViewSelectors from './document-view.selectors'
import { DocumentViewState } from './document-view.reducer'
import { ViewModel } from '@venio/data-access/review'
import { ConditionGroup } from '@venio/shared/models/interfaces'

type DocumentViewStateKeys =
  | keyof DocumentViewState
  | Array<keyof DocumentViewState>
@Injectable({ providedIn: 'root' })
export class DocumentViewFacade {
  public readonly selectIsDocumentViewListLoading$ = this.store.pipe(
    select(
      DocumentViewSelectors.getStateFromDocumentViewStore(
        'isDocumentViewListLoading'
      )
    )
  )

  public selectIsSearchFieldLoading$ = this.store.pipe(
    select(
      DocumentViewSelectors.getStateFromDocumentViewStore(
        'isSearchFieldLoading'
      )
    )
  )

  public readonly selectDocumentViewListSuccessResponse$ = this.store.pipe(
    select(
      DocumentViewSelectors.getStateFromDocumentViewStore(
        'documentViewListSuccessResponse'
      )
    )
  )

  public readonly selectDocumentViewListErrorResponse$ = this.store.pipe(
    select(
      DocumentViewSelectors.getStateFromDocumentViewStore(
        'documentViewListErrorResponse'
      )
    )
  )

  public readonly selectSearchFields$ = this.store.pipe(
    select(DocumentViewSelectors.getStateFromDocumentViewStore('searchFields'))
  )

  public readonly selectSearchFieldsErrorResponse$ = this.store.pipe(
    select(
      DocumentViewSelectors.getStateFromDocumentViewStore(
        'searchFieldsErrorResponse'
      )
    )
  )

  public readonly selectSelectedDocumentView$ = this.store.pipe(
    select(
      DocumentViewSelectors.getStateFromDocumentViewStore(
        'selectedDocumentView'
      )
    )
  )

  public readonly selectCurrentFormData$ = this.store.pipe(
    select(
      DocumentViewSelectors.getStateFromDocumentViewStore('currentFormData')
    )
  )

  public readonly selectIsViewAddOrUpdateLoading$ = this.store.pipe(
    select(
      DocumentViewSelectors.getStateFromDocumentViewStore(
        'isViewAddOrUpdateLoading'
      )
    )
  )

  public readonly selectAddOrUpdateViewSuccessResponse$ = this.store.pipe(
    select(
      DocumentViewSelectors.getStateFromDocumentViewStore(
        'addOrUpdateViewSuccessResponse'
      )
    )
  )

  public readonly selectAddOrUpdateViewErrorResponse$ = this.store.pipe(
    select(
      DocumentViewSelectors.getStateFromDocumentViewStore(
        'addOrUpdateViewErrorResponse'
      )
    )
  )

  public selectConditionGroup$ = this.store.pipe(
    select(
      DocumentViewSelectors.getStateFromDocumentViewStore('conditionGroup')
    )
  )

  constructor(private readonly store: Store) {}

  public fetchDocumentViewList(): void {
    this.store.dispatch(DocumentViewActions.fetchDocumentViewList({}))
  }

  public fetchSearchFields(): void {
    this.store.dispatch(DocumentViewActions.fetchSearchFields())
  }

  public addOrUpdateView(view: ViewModel, shouldLoadAndApply = false): void {
    this.store.dispatch(
      DocumentViewActions.addOrUpdateView({ view, shouldLoadAndApply })
    )
  }

  public fetchViewByViewId(viewId: number): void {
    this.store.dispatch(DocumentViewActions.fetchViewByViewId({ viewId }))
  }

  /**
   * Deletes a document view by id.
   * @param {number} viewId - The ID of the view to delete.
   * @returns {void}
   */
  public deleteDocumentView(viewId: number): void {
    this.store.dispatch(DocumentViewActions.deleteDocumentView({ viewId }))
  }

  /**
   * Bulk delete document views by ids
   * @param {number[]} viewIds
   */
  public deleteDocumentViews(viewIds: number[]): void {
    this.store.dispatch(DocumentViewActions.deleteDocumentViews({ viewIds }))
  }

  /**
   * Resetting the Document View List State
   * @returns {void}
   */
  public resetDocumentViewListState(): void {
    this.store.dispatch(
      DocumentViewActions.resetDocumentViewState({
        stateKey: [
          'selectedDocumentView',
          'isDocumentViewListLoading',
          'documentViewListErrorResponse',
          'documentViewListSuccessResponse',
        ],
      })
    )
  }

  public storeSelectedDocumentView(selectedDocumentView: ViewModel): void {
    this.store.dispatch(
      DocumentViewActions.storeSelectedDocumentView({ selectedDocumentView })
    )
  }

  public storeCurrentFormData(currentFormData: Partial<ViewModel>): void {
    this.store.dispatch(
      DocumentViewActions.storeCurrentFormData({ currentFormData })
    )
  }

  public storeConditionGroup(conditionGroup: ConditionGroup[]): void {
    this.store.dispatch(
      DocumentViewActions.storeConditionGroup({ conditionGroup })
    )
  }

  /**
   * Resetting a specific property of the Document View State
   * @param {DocumentViewStateKeys} stateKey - state keys
   * @returns {void}
   */
  public resetDocumentViewState(stateKey: DocumentViewStateKeys): void {
    this.store.dispatch(
      DocumentViewActions.resetDocumentViewState({ stateKey })
    )
  }
}
