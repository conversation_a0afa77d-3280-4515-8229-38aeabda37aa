import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DocumentTagComponent } from './document-tag.component'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { DocumentTagFacade } from '@venio/data-access/document-utility'
import {
  DocumentsFacade,
  FieldFacade,
  ReviewFacade,
  SearchFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { TagsTreeWrapperComponent } from '../tags-tree-wrapper/tags-tree-wrapper.component'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import { ActivatedRoute } from '@angular/router'
import { of } from 'rxjs'
import { DocumentTagTreeListToggle } from '@venio/shared/models/constants'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { VenioNotificationService } from '@venio/feature/notification'

describe('DocumentTagComponent', () => {
  let component: DocumentTagComponent
  let fixture: ComponentFixture<DocumentTagComponent>

  beforeEach(async () => {
    // Mock dependencies
    const documentTagFacadeMock = {
      selectFilterDocumentTags$: of(''),
      selectExpandedTagIds$: of([]),
      selectReloadTagGroup$: of(false),
      fetchProjectTags: jest.fn().mockReturnValue(
        of([
          { id: 1, name: 'Tag1' },
          { id: 2, name: 'Tag2' },
        ])
      ),
      selectProjectTags$: of([
        { id: 1, name: 'Tag1' },
        { id: 2, name: 'Tag2' },
      ]),
      fetchTagSettings: jest.fn(),
      selectFlatProjectTags$: of([]),
      selectDocumentTags$: of([]),
      selectFilteredProjectTags$: of([]),
      selectDocumentProjectTagGroup$: of([]),
      applyDocumentTagErrorResponse$: of([]),
      selectShowTagRuleList$: of([]),
      selectIsDocumentTagUpdated$: of([]),
      selectTagAddOrUpdateSuccessResponse$: of(null),
      setExpandedTagIds: jest.fn(),
      fetchTagRuleList: jest.fn(),
      fetchTagRuleDescription: jest.fn(),
      resetDocumentTagState: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [
        BrowserAnimationsModule,
        DocumentTagComponent,
        TagsTreeWrapperComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        { provide: DocumentTagFacade, useValue: documentTagFacadeMock },
        SearchFacade,
        FieldFacade,
        ReviewFacade,
        StartupsFacade,
        {
          provide: VenioNotificationService,
          useValue: {
            showSuccess: jest.fn(),
            showError: jest.fn(),
            showWarning: jest.fn(),
          },
        },
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentTagComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    // GIVEN the DocumentTagComponent is initialized
    // WHEN the component is created
    // THEN the component instance should be truthy (exists and is not null)
    expect(component).toBeTruthy()
  })

  it('should toggle expand all tags', () => {
    // GIVEN the initial state of the tag list is collapsed
    expect(component.isTagListExpanded).toBeFalsy()
    const expandedTagIds = [1, 2, 3]
    component.expandedClonedKeys = expandedTagIds

    // WHEN the toggleExpandCollapseAll method is called with EXPAND_ALL
    component.toggleExpandCollapseAll(DocumentTagTreeListToggle.EXPAND_ALL)

    // THEN the isTagListExpanded should be true and expandedKeys should match expandedTagIds
    expect(component.isTagListExpanded).toBeTruthy()
    expect(component.expandedKeys).toEqual(expandedTagIds)
  })

  it('should toggle collapse all tags', () => {
    // GIVEN the initial state of the tag list is expanded
    component.toggleExpandCollapseAll(DocumentTagTreeListToggle.EXPAND_ALL)
    expect(component.isTagListExpanded).toBeTruthy()

    // WHEN the toggleExpandCollapseAll method is called with COLLAPSE_ALL
    component.toggleExpandCollapseAll(DocumentTagTreeListToggle.COLLAPSE_ALL)

    // THEN the isTagListExpanded should be false
    expect(component.isTagListExpanded).toBeFalsy()
  })

  it('should show expand and collapse button', () => {
    // GIVEN the initial state of the tag list is collapsed
    expect(component.showExpandCollapse()).toBeFalsy()
    const mockTeeNodes = [
      { id: 1, name: 'Tag1' },
      { id: 2, name: 'Tag2' },
    ]
    component.treeNodes = mockTeeNodes

    // WHEN the toggleExpandCollapseAll method is called with EXPAND_ALL
    component.setExpandCollapseVisibility()
    // THEN Check if showExpandCollapse should be true and treeNodes should match treeNodes
    expect(component.showExpandCollapse()).toBeTruthy()
    expect(component.treeNodes).toEqual(mockTeeNodes)
  })

  it('should hide expand and collapse button', () => {
    // GIVEN the component is initialized with an empty tree node list
    expect(component.showExpandCollapse()).toBeFalsy()
    component.treeNodes = []

    // WHEN the method to set the expand collapse visibility is called
    component.setExpandCollapseVisibility()

    // THEN Check if showExpandCollapse should be false
    expect(component.showExpandCollapse()).toBeFalsy()
  })
})
