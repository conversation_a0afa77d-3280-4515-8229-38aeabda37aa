<div class="t-flex t-flex-col t-gap-4 t-flex-1">
  <!-- Titlebar template for child windows -->
  <ng-template #windowTitleBar let-win>
    <span class="k-window-title t-items-center t-text-primary t-text-lg">
      <button
        class="t-inline-block t-cursor-default t-pointer-events-none t-w-[35px] t-p-1.5 t-bg-[#F3F3F3] t-rounded-full t-mr-2 t-h-[35px] t-items-center"
        fillMode="clear"
        kendoButton
        imageUrl="assets/svg/icon-view-management.svg"></button>
      View
    </span>
    <button
      kendoWindowCloseAction
      [window]="win"
      class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-w-6 t-h-6 t-p-0"></button>
  </ng-template>
  <!-- Listing Grid -->
  <kendo-grid
    #grid
    class="t-flex t-w-full t-h-full t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
    [kendoGridBinding]="filteredViews()"
    venioDynamicHeight
    [sortable]="true"
    [groupable]="false"
    [reorderable]="true"
    [pageable]="{ type: 'numeric', position: 'top' }"
    [resizable]="true"
    kendoGridSelectBy="viewId"
    [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
    [selectedKeys]="selectedViewIds()"
    (selectedKeysChange)="onSelectedKeysChange($event)"
    [filterable]="false">
    <ng-template kendoPagerTemplate>
      <div class="t-flex t-gap-2">
        <kendo-textbox
          class="!t-border-[#ccc] !t-w-[25rem]"
          placeholder="Search"
          [clearButton]="true"
          (valueChange)="onSearchChange($event)">
          <ng-template kendoTextBoxSuffixTemplate>
            <button
              kendoButton
              fillMode="clear"
              class="t-text-[#1EBADC]"
              imageUrl="assets/svg/icon-updated-search.svg"></button>
          </ng-template>
        </kendo-textbox>
      </div>
      <kendo-grid-spacer></kendo-grid-spacer>

      <div class="t-flex t-gap-2">
        <button
          kendoButton
          fillMode="outline"
          title="Delete"
          #deleteBtn
          *ngIf="hasViewManageRight() && selectedViewIds()?.length > 0"
          (click)="onDeleteSelected()"
          type="button"
          rounded="medium"
          class="t-w-8 t-px-6 hover:t-border-[#ED7425] hover:!t-bg-[#ED7425]">
          <span
            venioSvgLoader
            applyEffectsTo="fill"
            hoverColor="#FFFFFF"
            color="#979797"
            [parentElement]="deleteBtn.element"
            svgUrl="assets/svg/Icon-material-delete.svg"
            height="1rem"
            width="1rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>

        <button
          kendoButton
          class="v-custom-secondary-button"
          themeColor="secondary"
          fillMode="outline"
          (click)="openCreateForm()"
          *ngIf="hasViewManageRight()"
          data-qa="create-new">
          Create New
        </button>
      </div>
    </ng-template>

    <kendo-grid-column
      title="#"
      [width]="35"
      headerClass="t-text-primary !t-pr-0.5">
      <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
        {{ rowIndex + 1 }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-checkbox-column
      [showSelectAll]="false"
      [width]="15"
      [autoSize]="false"
      [resizable]="false"
      class="!t-px-0.5">
      <ng-template kendoGridCellTemplate let-dataItem>
        <input
          type="checkbox"
          kendoCheckBox
          [checked]="selectedViewIds().includes(dataItem.viewId)"
          [disabled]="isSelectionDisabled(dataItem)"
          (change)="onToggleRowSelection(dataItem, $event)" />
      </ng-template>
    </kendo-grid-checkbox-column>

    <kendo-grid-column
      field="viewName"
      title="Name"
      [width]="240"
      headerClass="t-text-primary">
      <ng-template kendoGridCellTemplate let-dataItem>
        <span class="t-text-[var(--v-custom-sky-blue)]">{{
          dataItem.viewName
        }}</span>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [width]="320"
      title="Created By & On"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate>
        <span class="t-text-ellipsis t-overflow-hidden" title="Created By & On"
          >Created By & On</span
        >
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-flex t-flex-col t-gap-0.5">
          <span>
            {{ dataItem?.createdByName || dataItem?.createdBy || '' }}
            <span class="t-text-xs t-text-[#999999]">{{
              dataItem?.createdDate || ''
            }}</span>
          </span>
          <span class="t-text-xs t-text-[#999999]">{{
            dataItem?.createdTime || ''
          }}</span>
        </div>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [width]="100"
      title="Actions"
      headerClass="t-text-primary">
      <ng-template kendoGridCellTemplate let-dataItem>
        <kendo-buttongroup>
          <button
            kendoButton
            #actionGrid2
            *ngIf="canManageRow(dataItem)"
            class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-none t-rounded-br-none t-w-[33px] hover:t-border-[#9BD2A7] hover:t-bg-[#9BD2A7]"
            (click)="onRowAction(dataItem, commonActionTypes.EDIT)"
            type="button"
            kendoTooltip
            title="Edit"
            size="none">
            <span
              [parentElement]="actionGrid2.element"
              venioSvgLoader
              hoverColor="#FFFFFF"
              color="#979797"
              svgUrl="assets/svg/icon-pencil-svg.svg"
              height="0.85rem"
              width="0.85rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
          <button
            kendoButton
            #actionGrid3
            *ngIf="canManageRow(dataItem)"
            class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-none t-rounded-br-none t-w-[33px] hover:t-border-[#2F3080] hover:t-bg-[#2F3080]"
            (click)="onRowAction(dataItem, commonActionTypes.CLONE)"
            type="button"
            kendoTooltip
            title="Clone"
            size="none">
            <span
              [parentElement]="actionGrid3.element"
              venioSvgLoader
              applyEffectsTo="fill"
              hoverColor="#FFFFFF"
              color="#979797"
              svgUrl="assets/svg/icon-add-clone.svg"
              height="0.75rem"
              width="0.8rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
          <button
            kendoButton
            #actionGrid4
            *ngIf="canManageRow(dataItem)"
            class="!t-py-[0.38rem] !t-px-[0.5rem] t-rounded-tr-sm t-rounded-br-sm t-w-[33px] hover:t-border-[#ED7425] hover:t-bg-[#ED7425]"
            (click)="onRowAction(dataItem, commonActionTypes.DELETE)"
            type="button"
            kendoTooltip
            title="Delete"
            size="none">
            <span
              [parentElement]="actionGrid4.element"
              venioSvgLoader
              hoverColor="#FFFFFF"
              color="#979797"
              applyEffectsTo="fill"
              svgUrl="assets/svg/Icon-material-delete.svg"
              height="0.75rem"
              width="0.8rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
        </kendo-buttongroup>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>

  <!-- Dialog container for confirmations (delete, etc.) -->
  <div #dialogContainer kendoDialogContainer></div>
</div>
